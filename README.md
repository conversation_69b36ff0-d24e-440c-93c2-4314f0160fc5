# VideoSeek Mobile

一个美观优雅的移动端知识管理应用，采用Flutter开发。

## 功能特性

### 🏠 首页设计
- **搜索栏**: 顶部搜索框，点击进入专门的搜索页面
- **用户头像**: 右侧头像按钮，点击进入个人中心
- **欢迎模块**: 渐变背景的欢迎横幅，包含LOGO、欢迎文字和聊天按钮
- **知识库板块**: 横向滑动的知识库卡片列表
- **笔记板块**: 瀑布流布局的笔记卡片展示
- **下拉刷新**: 支持下拉刷新更新内容数据

### 🔍 搜索页面
- **专门搜索界面**: 点击首页搜索框进入独立搜索页面
- **实时搜索**: 输入关键词实时显示搜索结果
- **清除按钮**: 输入文字后显示优雅的圆形清除按钮
- **取消功能**: 右侧取消按钮返回首页
- **结果分类**: 分别显示知识库和笔记搜索结果
- **空状态**: 优雅的空状态和无结果提示

### 📚 知识库功能
- 显示知识库标题、描述和封面
- 展示内容数量和使用人数统计
- 显示创建者信息
- 支持横向滑动浏览

### 📝 笔记功能
- 瀑布流布局，自适应卡片高度
- 支持图片展示
- 标签系统
- 点赞和评论数统计
- 作者信息展示

### 👤 个人中心
- 用户基本信息展示
- 关注者/关注中统计
- 功能菜单（收藏、历史、设置等）

### 💬 对话页面
- **AI助手对话**: 与VideoSeek AI助手进行智能对话
- **消息气泡**: 美观的消息气泡设计，区分用户和AI消息
- **实时交互**: 支持发送消息和接收AI回复
- **空状态**: 优雅的对话开始引导界面

## 📁 项目结构

### 🏗️ 整体架构
项目采用分层架构设计，按功能模块组织代码，便于维护和扩展。

```
lib/
├── main.dart                 # 🚀 应用入口文件
├── models/                   # 📊 数据模型层
│   ├── knowledge_base.dart   # 知识库数据模型
│   ├── note.dart            # 笔记数据模型
│   └── user.dart            # 用户数据模型
├── pages/                    # 📱 页面层 (UI界面)
│   ├── auth/                # 🔐 认证相关页面
│   │   ├── login_page.dart          # 登录页面
│   │   └── register_page.dart       # 注册页面
│   ├── home/                # 🏠 首页模块
│   │   └── home_page.dart           # 应用主页
│   ├── search/              # 🔍 搜索模块
│   │   └── search_page.dart         # 搜索页面
│   ├── chat/                # 💬 对话模块
│   │   └── chat_page.dart           # AI对话页面
│   ├── knowledge_base/      # 📚 知识库模块
│   │   ├── knowledge_base_list_page.dart    # 知识库列表
│   │   ├── knowledge_base_detail_page.dart  # 知识库详情
│   │   └── knowledge_base_search_page.dart  # 知识库搜索
│   ├── note/                # 📝 笔记模块
│   │   └── note_detail_page.dart    # 笔记详情页面
│   ├── profile/             # 👤 个人中心模块
│   │   └── profile_page.dart        # 个人资料页面
│   └── common/              # 🔧 通用页面
│       └── webview_page.dart        # 网页浏览器页面
├── widgets/                  # 🧩 组件层 (可复用UI组件)
│   ├── home/                # 🏠 首页专用组件
│   │   ├── search_header.dart           # 搜索头部组件
│   │   ├── welcome_banner.dart          # 欢迎横幅组件
│   │   ├── knowledge_base_card.dart     # 知识库卡片
│   │   ├── knowledge_base_section.dart  # 知识库板块
│   │   ├── note_card.dart               # 笔记卡片
│   │   ├── notes_section.dart           # 笔记板块
│   │   ├── floating_action_buttons.dart # 浮动操作按钮
│   │   ├── note_creation_bottom_sheet.dart # 笔记创建底部弹窗
│   │   ├── note_filter_bottom_sheet.dart   # 笔记筛选底部弹窗
│   │   └── note_sort_bottom_sheet.dart     # 笔记排序底部弹窗
│   ├── search/              # 🔍 搜索页面专用组件
│   │   ├── search_header.dart       # 搜索页面头部
│   │   ├── search_result_item.dart  # 搜索结果项
│   │   └── search_results_list.dart # 搜索结果列表
│   ├── knowledge_base/      # 📚 知识库专用组件
│   │   ├── knowledge_base_list_item.dart        # 知识库列表项
│   │   ├── knowledge_base_detail_header.dart    # 知识库详情头部
│   │   ├── knowledge_base_content_tabs.dart     # 知识库内容标签页
│   │   ├── knowledge_base_action_bottom_sheet.dart # 知识库操作底部弹窗
│   │   └── create_knowledge_base_bottom_sheet.dart # 创建知识库底部弹窗
│   ├── note/                # 📝 笔记专用组件
│   │   ├── note_action_bottom_sheet.dart # 笔记操作底部弹窗
│   │   └── note_append_bottom_sheet.dart # 笔记追加底部弹窗
│   └── common/              # 🔄 通用组件 (跨模块复用)
│       ├── loading_indicator.dart   # 加载指示器
│       ├── empty_state.dart         # 空状态组件
│       └── more_button.dart         # 更多操作按钮 (统一样式)
├── services/                 # 🔧 服务层 (业务逻辑)
│   ├── auth_service.dart     # 认证服务
│   └── mock_data_service.dart # 模拟数据服务
├── utils/                    # 🛠️ 工具类
│   └── time_utils.dart       # 时间处理工具
└── theme/                    # 🎨 主题样式
    └── app_theme.dart       # 应用主题配置 (颜色、字体、样式)
```

### 🏛️ 架构设计原则

#### 📂 分层架构
- **页面层 (Pages)**: 负责UI界面展示和用户交互
- **组件层 (Widgets)**: 可复用的UI组件，按功能模块组织
- **服务层 (Services)**: 处理业务逻辑和数据操作
- **模型层 (Models)**: 定义数据结构和类型
- **工具层 (Utils)**: 提供通用工具函数
- **主题层 (Theme)**: 统一管理应用样式

#### 🔄 组件复用策略
- **通用组件**: 放在 `widgets/common/` 下，可跨模块使用
- **专用组件**: 按功能模块分类，如 `widgets/home/<USER>/note/` 等
- **统一样式**: 通过 `MoreButton` 等通用组件确保UI一致性

#### 📱 模块化设计
- **功能独立**: 每个模块相对独立，便于维护和测试
- **清晰边界**: 模块间通过明确的接口进行交互
- **易于扩展**: 新功能可以轻松添加到对应模块

#### 📋 代码组织最佳实践
- **命名规范**: 文件名使用下划线分隔，类名使用驼峰命名
- **导入顺序**: Flutter包 → 第三方包 → 项目内部包
- **组件拆分**: 复杂页面拆分为多个小组件，提高可读性
- **状态管理**: 合理使用StatefulWidget和StatelessWidget
- **注释文档**: 关键组件和方法添加详细注释

## 设计特点

### 🎨 UI设计
- **现代化设计**: 采用Material Design 3规范
- **优雅配色**: 主色调为紫色系，营造专业感
- **圆角设计**: 统一的12px圆角，提升视觉体验
- **阴影效果**: 适度的阴影增强层次感

### 📱 响应式布局
- **瀑布流**: 笔记采用瀑布流布局，充分利用屏幕空间
- **横向滚动**: 知识库支持横向滑动浏览
- **自适应**: 组件自适应不同屏幕尺寸
- **下拉刷新**: 流畅的下拉刷新交互体验

### 🔧 代码架构
- **模块化**: 清晰的文件结构，便于维护
- **组件化**: 可复用的UI组件
- **数据分离**: 模型、视图、服务分离
- **主题统一**: 集中的主题管理

## 技术栈

- **Flutter**: 跨平台移动应用开发框架
- **Dart**: 编程语言
- **flutter_staggered_grid_view**: 瀑布流布局库
- **Material Design 3**: UI设计规范

## 运行项目

1. 确保已安装Flutter SDK
2. 克隆项目到本地
3. 安装依赖：
   ```bash
   flutter pub get
   ```
4. 运行项目：
   ```bash
   flutter run
   ```

## 测试

运行测试：
```bash
flutter test
```

## 🚀 项目优化建议

### 📈 性能优化
- **懒加载**: 对大列表实现懒加载，提升滚动性能
- **图片缓存**: 实现图片缓存机制，减少网络请求
- **状态管理**: 考虑引入Provider或Riverpod进行状态管理
- **代码分割**: 按路由进行代码分割，减少初始包大小

### 🔧 代码质量
- **单元测试**: 为关键业务逻辑添加单元测试
- **集成测试**: 添加端到端测试确保功能完整性
- **代码规范**: 使用dart_code_metrics进行代码质量检查
- **文档完善**: 为公共API添加详细的文档注释

### 🛡️ 安全性增强
- **数据加密**: 敏感数据本地存储加密
- **网络安全**: 实现证书绑定和请求签名
- **权限管理**: 细化用户权限控制
- **输入验证**: 加强用户输入验证和过滤

### 🌐 国际化支持
- **多语言**: 实现i18n国际化支持
- **本地化**: 适配不同地区的文化习惯
- **RTL支持**: 支持从右到左的语言布局

## 后续扩展

- [ ] 实现真实的数据接口
- [ ] 添加搜索功能
- [ ] 实现知识库详情页
- [ ] 实现笔记详情页
- [ ] 添加用户认证
- [ ] 实现离线缓存
- [ ] 添加推送通知
- [ ] 支持深色模式
- [ ] 添加单元测试和集成测试
- [ ] 实现状态管理方案
- [ ] 优化图片加载和缓存
- [ ] 添加国际化支持

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License

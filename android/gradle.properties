org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# 优化构建速度
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=true

# 网络优化
systemProp.http.proxyHost=
systemProp.http.proxyPort=
systemProp.https.proxyHost=
systemProp.https.proxyPort=

# 下载优化
org.gradle.internal.http.connectionTimeout=60000
org.gradle.internal.http.socketTimeout=60000
systemProp.http.keepAlive=true
systemProp.http.maxConnections=10
systemProp.http.maxConnectionsPerRoute=10

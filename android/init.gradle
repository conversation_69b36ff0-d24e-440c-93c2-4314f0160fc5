// 配置阿里云镜像加速下载
def ALIYUN_REPOSITORY_URL = 'https://maven.aliyun.com/repository/public'
def ALIYUN_JCENTER_URL = 'https://maven.aliyun.com/repository/jcenter'
def ALIYUN_GOOGLE_URL = 'https://maven.aliyun.com/repository/google'
def ALIYUN_GRADLE_PLUGIN_URL = 'https://maven.aliyun.com/repository/gradle-plugin'

allprojects {
    repositories {
        all { repo ->
            if(repo.class.simpleName == 'DefaultMavenArtifactRepository' || repo.hasProperty('url')){
                def url = repo.url.toString()
                // Maven Central 的各种 URL 格式
                if (url.startsWith('https://repo1.maven.org/maven2') ||
                    url.startsWith('https://repo.maven.apache.org/maven2') ||
                    url.startsWith('https://central.maven.org/maven2') ||
                    url.contains('maven.org') ||
                    url.contains('maven.apache.org')) {
                    project.logger.lifecycle "Repository ${repo.url} replaced by $ALIYUN_REPOSITORY_URL."
                    remove repo
                }
                // JCenter
                if (url.startsWith('https://jcenter.bintray.com/') || url.contains('jcenter')) {
                    project.logger.lifecycle "Repository ${repo.url} replaced by $ALIYUN_JCENTER_URL."
                    remove repo
                }
                // Google Maven
                if (url.startsWith('https://dl.google.com/dl/android/maven2/') ||
                    url.startsWith('https://maven.google.com/') ||
                    url.contains('maven.google.com')) {
                    project.logger.lifecycle "Repository ${repo.url} replaced by $ALIYUN_GOOGLE_URL."
                    remove repo
                }
                // Gradle Plugin Portal
                if (url.startsWith('https://plugins.gradle.org/m2/') || url.contains('plugins.gradle.org')) {
                    project.logger.lifecycle "Repository ${repo.url} replaced by $ALIYUN_GRADLE_PLUGIN_URL."
                    remove repo
                }
            }
        }
        // 优先使用阿里云镜像
        maven { url ALIYUN_GRADLE_PLUGIN_URL }
        maven { url ALIYUN_REPOSITORY_URL }
        maven { url ALIYUN_JCENTER_URL }
        maven { url ALIYUN_GOOGLE_URL }
    }
}

// 配置插件仓库镜像
settingsEvaluated { settings ->
    settings.pluginManagement {
        repositories {
            maven { url ALIYUN_GRADLE_PLUGIN_URL }
            maven { url ALIYUN_REPOSITORY_URL }
            maven { url ALIYUN_GOOGLE_URL }
            gradlePluginPortal()
            google()
            mavenCentral()
        }
    }
}

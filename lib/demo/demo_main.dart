import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'photo_note_demo.dart';

void main() {
  runApp(const DemoApp());
}

class DemoApp extends StatelessWidget {
  const DemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '拍照笔记演示',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
        useMaterial3: true,
      ),
      home: const PhotoNoteDemoPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

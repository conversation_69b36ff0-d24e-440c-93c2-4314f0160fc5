import 'package:flutter/material.dart';
import 'theme/app_theme.dart';
import 'pages/home/<USER>';

void main() {
  runApp(const VideoSeekApp());
}

class VideoSeekApp extends StatelessWidget {
  const VideoSeekApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VideoSeek',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,  // 跟随系统主题
      home: const HomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

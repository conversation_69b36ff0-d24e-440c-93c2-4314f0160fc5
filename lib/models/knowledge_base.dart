enum KnowledgeBasePermission {
  public,   // 公开
  team,     // 团队
  private,  // 私密
}

class KnowledgeBase {
  final String id;
  final String title;
  final int contentCount;
  final int userCount;
  final String author;
  final String authorAvatar;
  final String description;
  final DateTime createdAt;
  final String? coverUrl;
  final bool isPublic;
  final KnowledgeBasePermission permission;

  const KnowledgeBase({
    required this.id,
    required this.title,
    required this.contentCount,
    required this.userCount,
    required this.author,
    required this.authorAvatar,
    required this.description,
    required this.createdAt,
    this.coverUrl,
    this.isPublic = false,
    this.permission = KnowledgeBasePermission.private,
  });

  factory KnowledgeBase.fromJson(Map<String, dynamic> json) {
    return KnowledgeBase(
      id: json['id'],
      title: json['title'],
      contentCount: json['contentCount'],
      userCount: json['userCount'],
      author: json['author'] ?? '',
      authorAvatar: json['authorAvatar'] ?? '',
      description: json['description'],
      createdAt: DateTime.parse(json['createdAt']),
      coverUrl: json['coverUrl'],
      isPublic: json['isPublic'] ?? false,
      permission: KnowledgeBasePermission.values.firstWhere(
        (e) => e.name == json['permission'],
        orElse: () => KnowledgeBasePermission.private,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'contentCount': contentCount,
      'userCount': userCount,
      'author': author,
      'authorAvatar': authorAvatar,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'coverUrl': coverUrl,
      'isPublic': isPublic,
      'permission': permission.name,
    };
  }
}

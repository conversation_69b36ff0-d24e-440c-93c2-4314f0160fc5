class User {
  final String id;
  final String name;
  final String email;
  final String avatar;
  final String bio;
  final int followersCount;
  final int followingCount;
  final DateTime joinedAt;

  const User({
    required this.id,
    required this.name,
    required this.email,
    required this.avatar,
    required this.bio,
    required this.followersCount,
    required this.followingCount,
    required this.joinedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      avatar: json['avatar'],
      bio: json['bio'] ?? '',
      followersCount: json['followersCount'] ?? 0,
      followingCount: json['followingCount'] ?? 0,
      joinedAt: DateTime.parse(json['joinedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatar': avatar,
      'bio': bio,
      'followersCount': followersCount,
      'followingCount': followingCount,
      'joinedAt': joinedAt.toIso8601String(),
    };
  }
}

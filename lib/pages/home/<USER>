import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../models/note.dart';
import '../../models/user.dart';
import '../../services/mock_data_service.dart';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../theme/app_theme.dart';
import '../profile/profile_page.dart';
import '../knowledge_base/knowledge_base_detail_page.dart';
import '../note/note_detail_page.dart';
import '../chat/chat_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late List<KnowledgeBase> knowledgeBases;
  late List<Note> notes;
  late User currentUser;
  String searchQuery = '';
  bool isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    knowledgeBases = MockDataService.getKnowledgeBases();
    notes = MockDataService.getNotes();
    currentUser = MockDataService.getCurrentUser();
  }

  Future<void> _onRefresh() async {
    setState(() {
      isRefreshing = true;
    });

    try {
      // 模拟网络请求延迟
      await Future.delayed(const Duration(seconds: 1));

      // 重新加载数据
      _loadData();

      // 显示刷新成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('刷新成功'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      // 处理刷新错误
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('刷新失败，请重试'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isRefreshing = false;
        });
      }
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      searchQuery = query;
    });
    // TODO: 实现搜索功能
    debugPrint('搜索: $query');
  }

  void _onProfileTap() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProfilePage(user: currentUser),
      ),
    );
  }

  void _onKnowledgeBaseTap(KnowledgeBase knowledgeBase) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => KnowledgeBaseDetailPage(
          knowledgeBase: knowledgeBase,
        ),
      ),
    );
  }

  void _onNoteTap(Note note) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => NoteDetailPage(note: note),
      ),
    );
  }

  void _onWelcomeBannerTap() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ChatPage(),
      ),
    );
  }

  void _onMoreTap() {
    NoteCreationBottomSheet.show(context);
  }

  void _onRecordTap() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('测试: 录音功能'),
        duration: Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _onTextTap() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('测试: 文字功能'),
        duration: Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Stack(
        children: [
          Column(
            children: [
              // 搜索头部
              SearchHeader(
                currentUser: currentUser,
                onProfileTap: _onProfileTap,
                onSearchChanged: _onSearchChanged,
              ),
              // 主要内容区域
              Expanded(
                child: RefreshIndicator(
                  onRefresh: _onRefresh,
                  color: AppTheme.primaryColor.withValues(alpha: 0.8),
                  backgroundColor: AppTheme.surfaceColor,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 16),
                        // 欢迎模块
                        WelcomeBanner(
                          onTap: _onWelcomeBannerTap,
                        ),
                        const SizedBox(height: 24),
                        // 知识库板块
                        KnowledgeBaseSection(
                          knowledgeBases: knowledgeBases,
                          onKnowledgeBaseTap: _onKnowledgeBaseTap,
                        ),
                        const SizedBox(height: 20),  // 减少知识库和笔记之间的间距
                        // 笔记板块
                        NotesSection(
                          notes: notes,
                          onNoteTap: _onNoteTap,
                        ),
                        const SizedBox(height: 120), // 为浮动按钮留出空间
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          // 浮动按钮
          FloatingActionButtons(
            onMoreTap: _onMoreTap,
            onRecordTap: _onRecordTap,
            onTextTap: _onTextTap,
          ),
        ],
      ),
    );
  }
}

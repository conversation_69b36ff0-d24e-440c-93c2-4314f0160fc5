import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../models/note.dart';
import '../../theme/app_theme.dart';

import '../../widgets/knowledge_base/knowledge_base_action_bottom_sheet.dart';
import '../../widgets/knowledge_base/knowledge_base_detail_header.dart';
import '../../widgets/knowledge_base/knowledge_base_content_tabs.dart';
import '../../widgets/common/ui/more_button.dart';
import '../../widgets/home/<USER>';
import '../../pages/note/note_detail_page.dart';

import 'knowledge_base_search_page.dart';

class KnowledgeBaseDetailPage extends StatefulWidget {
  final KnowledgeBase knowledgeBase;

  const KnowledgeBaseDetailPage({super.key, required this.knowledgeBase});

  @override
  State<KnowledgeBaseDetailPage> createState() =>
      _KnowledgeBaseDetailPageState();
}

class _KnowledgeBaseDetailPageState extends State<KnowledgeBaseDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // 模拟数据
  List<Map<String, dynamic>> _notes = [];
  List<Map<String, dynamic>> _files = [];
  List<Map<String, dynamic>> _authors = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadContentData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadContentData() {
    // 模拟笔记数据
    _notes = [
      {
        'id': '1',
        'title': 'Flutter状态管理详解',
        'content':
            '在Flutter开发中，状态管理是一个核心概念。本文将详细介绍Provider、Bloc、Riverpod等状态管理方案...',
        'publishTime': DateTime.now().subtract(const Duration(hours: 2)),
        'author': '小明',
      },
      {
        'id': '2',
        'title': 'Widget生命周期分析',
        'content': 'Flutter中的Widget有着复杂的生命周期，理解这些生命周期对于优化应用性能至关重要...',
        'publishTime': DateTime.now().subtract(const Duration(days: 1)),
        'author': '小红',
      },
    ];

    // 模拟文件数据
    _files = [
      {'id': '1', 'title': 'Flutter开发规范', 'contentCount': 15},
      {'id': '2', 'title': 'UI组件库', 'contentCount': 28},
    ];

    // 模拟订阅博主数据
    _authors = [
      {
        'id': '1',
        'name': 'Flutter官方',
        'avatar': 'https://picsum.photos/200/300?random=10',
        'noteCount': 156,
      },
      {
        'id': '2',
        'name': 'Google开发者',
        'avatar': 'https://picsum.photos/200/300?random=11',
        'noteCount': 89,
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          KnowledgeBaseDetailHeader(knowledgeBase: widget.knowledgeBase),
          _buildTabBar(),
          Expanded(child: _buildTabBarView()),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.backgroundColor,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppTheme.textPrimaryColor,
          size: 20,
        ),
      ),
      actions: [
        IconButton(
          onPressed: _openSearchPage,
          icon: const Icon(Icons.search, color: AppTheme.textPrimaryColor),
        ),
        IconButton(
          onPressed: _shareKnowledgeBase,
          icon: const Icon(Icons.share, color: AppTheme.textPrimaryColor),
        ),
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: MoreButton(onTap: _showActionBottomSheet),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppTheme.surfaceColor,
      child: TabBar(
        controller: _tabController,
        isScrollable: true, // 允许标签页滚动
        padding: EdgeInsets.zero, // 完全移除内边距
        tabAlignment: TabAlignment.start, // 标签左对齐
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: AppTheme.textSecondaryColor,
        indicatorColor: AppTheme.primaryColor,
        indicatorWeight: 2,
        labelPadding: const EdgeInsets.symmetric(horizontal: 16), // 恢复正常间距
        labelStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 16,
        ),
        tabs: [
          Tab(text: '全部(${_getTotalCount()})'),
          Tab(text: '笔记(${_notes.length})'),
          Tab(text: '文件(${_files.length})'),
          Tab(text: '订阅的博主(${_authors.length})'), // 简化文字
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildAllContent(),
        _buildNotesList(),
        _buildFilesList(),
        _buildAuthorsList(),
      ],
    );
  }

  Widget _buildAllContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (_notes.isNotEmpty) ...[
            _buildSectionTitle('最新笔记'),
            const SizedBox(height: 12),
            ..._notes.take(3).map((note) => _buildNoteItem(note)),
            const SizedBox(height: 24),
          ],
          if (_files.isNotEmpty) ...[
            _buildSectionTitle('文件夹'),
            const SizedBox(height: 12),
            ..._files.take(3).map((file) => _buildFileItem(file)),
            const SizedBox(height: 24),
          ],
          if (_authors.isNotEmpty) ...[
            _buildSectionTitle('订阅博主'),
            const SizedBox(height: 12),
            ..._authors.take(3).map((author) => _buildAuthorItem(author)),
          ],
        ],
      ),
    );
  }

  Widget _buildNotesList() {
    return KnowledgeBaseContentTabs(
      contentType: ContentType.notes,
      items: _notes,
      onItemTap: _onNoteTap,
      onItemAction: _showNoteActionBottomSheet,
    );
  }

  Widget _buildFilesList() {
    return KnowledgeBaseContentTabs(
      contentType: ContentType.files,
      items: _files,
      onItemTap: _onFileTap,
    );
  }

  Widget _buildAuthorsList() {
    return KnowledgeBaseContentTabs(
      contentType: ContentType.authors,
      items: _authors,
      onItemTap: _onAuthorTap,
    );
  }

  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildNoteItem(Map<String, dynamic> noteMap) {
    final note = _mapToNote(noteMap);
    return NoteCard(note: note, onTap: () => _onNoteTap(noteMap));
  }

  Widget _buildFileItem(Map<String, dynamic> file) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.folder,
              color: AppTheme.primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  file['title'],
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${file['contentCount']}个内容',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuthorItem(Map<String, dynamic> author) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
            backgroundImage:
                author['avatar'] != null
                    ? NetworkImage(author['avatar'])
                    : null,
            child:
                author['avatar'] == null
                    ? const Icon(
                      Icons.person,
                      color: AppTheme.primaryColor,
                      size: 24,
                    )
                    : null,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  author['name'],
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${author['noteCount']}条笔记',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  int _getTotalCount() {
    return _notes.length + _files.length + _authors.length;
  }

  // 将Map数据转换为Note对象
  Note _mapToNote(Map<String, dynamic> noteMap) {
    return Note(
      id: noteMap['id'] ?? '',
      title: noteMap['title'] ?? '',
      content: noteMap['content'] ?? '',
      author: noteMap['author'] ?? '知识库作者',
      authorAvatar:
          noteMap['authorAvatar'] ?? 'https://picsum.photos/200/300?random=1',
      images: List<String>.from(noteMap['images'] ?? []),
      tags: List<String>.from(noteMap['tags'] ?? []),
      likeCount: noteMap['likeCount'] ?? 0,
      commentCount: noteMap['commentCount'] ?? 0,
      createdAt: noteMap['publishTime'] ?? DateTime.now(),
      updatedAt:
          noteMap['updatedAt'] ?? noteMap['publishTime'] ?? DateTime.now(),
    );
  }

  void _openSearchPage() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const KnowledgeBaseSearchPage()),
    );
  }

  void _shareKnowledgeBase() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('分享知识库: ${widget.knowledgeBase.title}'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showActionBottomSheet() {
    KnowledgeBaseActionBottomSheet.show(context, widget.knowledgeBase);
  }

  void _onNoteTap(Map<String, dynamic> noteMap) {
    final note = _mapToNote(noteMap);
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => NoteDetailPage(note: note)));
  }

  void _onFileTap(Map<String, dynamic> file) {
    // TODO: 导航到文件夹详情页面
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('打开文件夹: ${file['title']}'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _onAuthorTap(Map<String, dynamic> author) {
    // TODO: 导航到博主详情页面
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('查看博主: ${author['name']}'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showNoteActionBottomSheet(Map<String, dynamic> note) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: const BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppTheme.borderColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),
                ListTile(
                  leading: const Icon(
                    Icons.share,
                    color: AppTheme.primaryColor,
                  ),
                  title: const Text('分享'),
                  onTap: () {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('分享笔记: ${note['title']}'),
                        duration: const Duration(seconds: 2),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.copy, color: AppTheme.primaryColor),
                  title: const Text('复制笔记'),
                  onTap: () {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('复制笔记: ${note['title']}'),
                        duration: const Duration(seconds: 2),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                ),
                const SizedBox(height: 32),
              ],
            ),
          ),
    );
  }
}

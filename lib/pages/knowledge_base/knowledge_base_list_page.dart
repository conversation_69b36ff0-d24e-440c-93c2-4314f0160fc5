import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../theme/app_theme.dart';
import '../../services/mock_data_service.dart';
import '../../widgets/knowledge_base/knowledge_base_list_item.dart';
import '../../widgets/knowledge_base/knowledge_base_action_bottom_sheet.dart';
import '../../widgets/knowledge_base/create_knowledge_base_bottom_sheet.dart';
import 'knowledge_base_search_page.dart';
import 'knowledge_base_detail_page.dart';

class KnowledgeBaseListPage extends StatefulWidget {
  const KnowledgeBaseListPage({super.key});

  @override
  State<KnowledgeBaseListPage> createState() => _KnowledgeBaseListPageState();
}

class _KnowledgeBaseListPageState extends State<KnowledgeBaseListPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // 数据列表
  late List<KnowledgeBase> _myKnowledgeBases;
  late List<KnowledgeBase> _subscribedKnowledgeBases;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  void _loadData() {
    _myKnowledgeBases = MockDataService.getMyKnowledgeBases();
    _subscribedKnowledgeBases = MockDataService.getSubscribedKnowledgeBases();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppTheme.textPrimaryColor,
            size: 20,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _openSearchPage(),
            icon: const Icon(
              Icons.search,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          IconButton(
            onPressed: () => CreateKnowledgeBaseBottomSheet.show(context),
            icon: const Icon(
              Icons.add,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondaryColor,
          indicatorColor: AppTheme.primaryColor,
          indicatorWeight: 2,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 16,
          ),
          tabs: const [
            Tab(text: '我创建的'),
            Tab(text: '我订阅的'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildKnowledgeBaseList(_myKnowledgeBases),
          _buildKnowledgeBaseList(_subscribedKnowledgeBases),
        ],
      ),
    );
  }

  Widget _buildKnowledgeBaseList(List<KnowledgeBase> knowledgeBases) {
    if (knowledgeBases.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_open,
              size: 64,
              color: AppTheme.textSecondaryColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              '暂无知识库',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: knowledgeBases.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final knowledgeBase = knowledgeBases[index];
        return KnowledgeBaseListItem(
          knowledgeBase: knowledgeBase,
          onTap: () => _onKnowledgeBaseTap(knowledgeBase),
          onMoreTap: () => _showActionBottomSheet(knowledgeBase),
        );
      },
    );
  }

  void _onKnowledgeBaseTap(KnowledgeBase knowledgeBase) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => KnowledgeBaseDetailPage(
          knowledgeBase: knowledgeBase,
        ),
      ),
    );
  }

  void _showActionBottomSheet(KnowledgeBase knowledgeBase) {
    KnowledgeBaseActionBottomSheet.show(context, knowledgeBase);
  }

  void _openSearchPage() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const KnowledgeBaseSearchPage(),
      ),
    );
  }
}

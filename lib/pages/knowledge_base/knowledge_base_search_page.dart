import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../theme/app_theme.dart';
import '../../services/mock_data_service.dart';
import '../../widgets/knowledge_base/knowledge_base_list_item.dart';
import '../../widgets/knowledge_base/knowledge_base_action_bottom_sheet.dart';

class KnowledgeBaseSearchPage extends StatefulWidget {
  const KnowledgeBaseSearchPage({super.key});

  @override
  State<KnowledgeBaseSearchPage> createState() => _KnowledgeBaseSearchPageState();
}

class _KnowledgeBaseSearchPageState extends State<KnowledgeBaseSearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  
  List<KnowledgeBase> _allKnowledgeBases = [];
  List<KnowledgeBase> _searchResults = [];
  List<String> _recentSearches = ['Flutter', 'UI设计', '人工智能', 'React'];
  bool _isSearching = false;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAllKnowledgeBases();
    _searchFocusNode.requestFocus();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _loadAllKnowledgeBases() {
    // 合并所有知识库数据
    _allKnowledgeBases = [
      ...MockDataService.getMyKnowledgeBases(),
      ...MockDataService.getSubscribedKnowledgeBases(),
      ...MockDataService.getPublicKnowledgeBases(),
    ];
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query.trim();
      _isSearching = _searchQuery.isNotEmpty;
      
      if (_isSearching) {
        _searchResults = _allKnowledgeBases.where((kb) {
          return kb.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                 kb.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                 kb.author.toLowerCase().contains(_searchQuery.toLowerCase());
        }).toList();
        
        // 添加到最近搜索（如果不存在）
        if (!_recentSearches.contains(_searchQuery)) {
          _recentSearches.insert(0, _searchQuery);
          if (_recentSearches.length > 10) {
            _recentSearches = _recentSearches.take(10).toList();
          }
        }
      } else {
        _searchResults.clear();
      }
    });
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _searchQuery = '';
      _isSearching = false;
      _searchResults.clear();
    });
    _searchFocusNode.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Column(
        children: [
          _buildSearchHeader(),
          Expanded(
            child: _isSearching ? _buildSearchResults() : _buildSearchSuggestions(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      decoration: _buildHeaderDecoration(),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            Expanded(child: _buildSearchBox()),
            const SizedBox(width: 12),
            _buildCancelButton(),
          ],
        ),
      ),
    );
  }

  BoxDecoration _buildHeaderDecoration() {
    return const BoxDecoration(
      color: AppTheme.surfaceColor,
      boxShadow: [
        BoxShadow(
          color: AppTheme.shadowColor,
          offset: Offset(0, 2),
          blurRadius: 8,
        ),
      ],
    );
  }

  Widget _buildSearchBox() {
    return Container(
      height: 44,
      decoration: _buildSearchBoxDecoration(),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        onChanged: _performSearch,
        decoration: InputDecoration(
          hintText: '搜索知识库...',
          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.textSecondaryColor,
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: AppTheme.primaryColor,
            size: 20,
          ),
          suffixIcon: _searchQuery.isNotEmpty ? _buildClearButton() : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppTheme.textPrimaryColor,
        ),
      ),
    );
  }

  BoxDecoration _buildSearchBoxDecoration() {
    return BoxDecoration(
      color: AppTheme.searchBoxColor,
      borderRadius: BorderRadius.circular(22),
      boxShadow: const [
        BoxShadow(
          color: AppTheme.searchBoxShadow,
          offset: Offset(0, 1),
          blurRadius: 4,
          spreadRadius: 0,
        ),
      ],
    );
  }

  Widget _buildClearButton() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: _clearSearch,
        child: Container(
          padding: const EdgeInsets.all(8),
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppTheme.textSecondaryColor.withValues(alpha: 0.08),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.close_rounded,
              color: AppTheme.textSecondaryColor,
              size: 12,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCancelButton() {
    return TextButton(
      onPressed: () => Navigator.of(context).pop(),
      child: Text(
        '取消',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_recentSearches.isNotEmpty) ...[
            _buildSectionTitle('最近搜索'),
            const SizedBox(height: 12),
            _buildSearchTags(_recentSearches, isRecent: true),
            const SizedBox(height: 24),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const Spacer(),
        if (title == '最近搜索')
          TextButton(
            onPressed: () {
              setState(() {
                _recentSearches.clear();
              });
            },
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              '清空',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSearchTags(List<String> tags, {bool isRecent = false}) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: tags.map((tag) {
        return GestureDetector(
          onTap: () {
            _searchController.text = tag;
            _performSearch(tag);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppTheme.borderColor,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isRecent)
                  const Padding(
                    padding: EdgeInsets.only(right: 4),
                    child: Icon(
                      Icons.history,
                      size: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                Text(
                  tag,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppTheme.textSecondaryColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              '未找到相关知识库',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '试试其他关键词',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Text(
                '找到 ${_searchResults.length} 个知识库',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _searchResults.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final knowledgeBase = _searchResults[index];
              return KnowledgeBaseListItem(
                knowledgeBase: knowledgeBase,
                onTap: () => _onKnowledgeBaseTap(knowledgeBase),
                onMoreTap: () => _showActionBottomSheet(knowledgeBase),
              );
            },
          ),
        ),
      ],
    );
  }

  void _onKnowledgeBaseTap(KnowledgeBase knowledgeBase) {
    // TODO: 导航到知识库详情页面
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('打开知识库: ${knowledgeBase.title}'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _showActionBottomSheet(KnowledgeBase knowledgeBase) {
    KnowledgeBaseActionBottomSheet.show(context, knowledgeBase);
  }
}

import 'package:flutter/material.dart';
import '../../models/note.dart';
import '../../theme/app_theme.dart';
import '../../utils/time_utils.dart';
import '../../widgets/common/bottom_sheets/note_operations/note_operations_bottom_sheet.dart';
import '../../widgets/common/bottom_sheets/note_creation/note_creation_options_bottom_sheet.dart';
import '../../widgets/note/add_to_knowledge_base_bottom_sheet.dart';
import '../../widgets/common/ui/more_button.dart';
import '../chat/chat_page.dart';

class NoteDetailPage extends StatefulWidget {
  final Note note;

  const NoteDetailPage({super.key, required this.note});

  @override
  State<NoteDetailPage> createState() => _NoteDetailPageState();
}

class _NoteDetailPageState extends State<NoteDetailPage> {
  // 模拟知识库关联状态
  String? _associatedKnowledgeBase;

  @override
  void initState() {
    super.initState();
    // 模拟检查笔记是否已关联知识库
    _checkKnowledgeBaseAssociation();
  }

  void _checkKnowledgeBaseAssociation() {
    // 这里应该从数据库或API检查笔记的知识库关联
    // 暂时模拟一些笔记有关联
    if (widget.note.id.hashCode % 3 == 0) {
      _associatedKnowledgeBase = 'Flutter开发指南';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButtons(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.backgroundColor,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppTheme.textPrimaryColor,
          size: 20,
        ),
      ),
      actions: [
        IconButton(
          onPressed: _shareNote,
          icon: const Icon(Icons.share, color: AppTheme.textPrimaryColor),
        ),
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: MoreButton(onTap: _showActionBottomSheet),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头部信息区域
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(24),
                bottomRight: Radius.circular(24),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 笔记标题
                Text(
                  widget.note.title,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                    height: 1.2,
                  ),
                ),
                const SizedBox(height: 16),

                // 元信息行
                Row(
                  children: [
                    // 知识库关联标签
                    _buildKnowledgeBaseSection(),
                    const Spacer(),
                    // 创建时间
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.backgroundColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        TimeUtils.formatPublishTime(widget.note.createdAt),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textTertiaryColor,
                          fontSize: 11,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // 笔记内容区域
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.04),
                  offset: const Offset(0, 2),
                  blurRadius: 12,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Text(
              widget.note.content,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppTheme.textPrimaryColor,
                height: 1.7,
                fontSize: 16,
                letterSpacing: 0.2,
              ),
            ),
          ),

          // 底部留出浮动按钮的空间
          const SizedBox(height: 120),
        ],
      ),
    );
  }

  Widget _buildKnowledgeBaseSection() {
    return GestureDetector(
      onTap:
          _associatedKnowledgeBase != null
              ? _navigateToKnowledgeBase
              : _showAddToKnowledgeBase,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              _associatedKnowledgeBase != null
                  ? AppTheme.primaryColor.withValues(alpha: 0.1)
                  : AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                _associatedKnowledgeBase != null
                    ? AppTheme.primaryColor.withValues(alpha: 0.2)
                    : AppTheme.borderColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _associatedKnowledgeBase != null
                  ? Icons.folder_rounded
                  : Icons.add_rounded,
              color:
                  _associatedKnowledgeBase != null
                      ? AppTheme.primaryColor
                      : AppTheme.textSecondaryColor,
              size: 16,
            ),
            const SizedBox(width: 6),
            Text(
              _associatedKnowledgeBase ?? '添加到知识库',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color:
                    _associatedKnowledgeBase != null
                        ? AppTheme.primaryColor
                        : AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            offset: const Offset(0, 4),
            blurRadius: 20,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          // 追加笔记按钮
          Expanded(
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: AppTheme.borderColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: _showAppendBottomSheet,
                  borderRadius: BorderRadius.circular(24),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_rounded,
                        size: 18,
                        color: AppTheme.textPrimaryColor,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        '追加笔记',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textPrimaryColor,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // AI助手按钮
          Expanded(
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryColor.withValues(alpha: 0.8),
                    AppTheme.secondaryColor.withValues(alpha: 0.8),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: _openAIAssistant,
                  borderRadius: BorderRadius.circular(24),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.auto_awesome_rounded,
                        size: 18,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'AI助手',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _shareNote() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('分享笔记: ${widget.note.title}'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showActionBottomSheet() {
    // 模拟知识库关联状态（实际应该从数据库获取）
    String? knowledgeBaseName = _associatedKnowledgeBase;

    NoteOperationsBottomSheet.show(
      context,
      widget.note,
      knowledgeBaseName: knowledgeBaseName,
    );
  }

  void _showAppendBottomSheet() {
    // 显示包含快速记录和更多方式的完整弹窗
    NoteCreationOptionsBottomSheet.show(context, showQuickRecord: true);
  }

  void _openAIAssistant() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ChatPage()));
  }

  void _navigateToKnowledgeBase() {
    // TODO: 导航到知识库详情页面
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('打开知识库: $_associatedKnowledgeBase'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        backgroundColor: AppTheme.primaryColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showAddToKnowledgeBase() {
    AddToKnowledgeBaseBottomSheet.show(context, widget.note);
  }
}

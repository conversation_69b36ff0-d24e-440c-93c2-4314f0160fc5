import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../models/note.dart';
import '../../services/mock_data_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/empty_state.dart';
import '../../widgets/search/search_header.dart';
import '../../widgets/search/search_results_list.dart';

class SearchPage extends StatefulWidget {
  final String initialQuery;

  const SearchPage({
    super.key,
    this.initialQuery = '',
  });

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  List<KnowledgeBase> _filteredKnowledgeBases = [];
  List<Note> _filteredNotes = [];
  bool _isSearching = false;
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    _currentQuery = widget.initialQuery;

    // 如果有初始查询，立即搜索
    if (widget.initialQuery.isNotEmpty) {
      _performSearch(widget.initialQuery);
    }
  }

  void _performSearch(String query) {
    setState(() {
      _isSearching = true;
      _currentQuery = query;
    });

    // 模拟搜索延迟
    Future.delayed(const Duration(milliseconds: 300), () {
      if (!mounted) return;
      
      final allKnowledgeBases = MockDataService.getKnowledgeBases();
      final allNotes = MockDataService.getNotes();
      
      if (query.isEmpty) {
        setState(() {
          _filteredKnowledgeBases = [];
          _filteredNotes = [];
          _isSearching = false;
        });
        return;
      }
      
      // 搜索知识库
      _filteredKnowledgeBases = allKnowledgeBases.where((kb) {
        return kb.title.toLowerCase().contains(query.toLowerCase()) ||
               kb.description.toLowerCase().contains(query.toLowerCase()) ||
               kb.author.toLowerCase().contains(query.toLowerCase());
      }).toList();
      
      // 搜索笔记
      _filteredNotes = allNotes.where((note) {
        return note.title.toLowerCase().contains(query.toLowerCase()) ||
               note.content.toLowerCase().contains(query.toLowerCase()) ||
               note.author.toLowerCase().contains(query.toLowerCase()) ||
               note.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
      }).toList();
      
      setState(() {
        _isSearching = false;
      });
    });
  }

  void _onSearchChanged(String query) {
    _performSearch(query);
  }

  void _onCancel() {
    Navigator.of(context).pop();
  }

  void _onKnowledgeBaseTap(KnowledgeBase knowledgeBase) {
    // TODO: 导航到知识库详情页面
    debugPrint('点击知识库: ${knowledgeBase.title}');
  }

  void _onNoteTap(Note note) {
    // TODO: 导航到笔记详情页面
    debugPrint('点击笔记: ${note.title}');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Column(
        children: [
          SearchPageHeader(
            initialQuery: widget.initialQuery,
            onSearchChanged: _onSearchChanged,
            onCancel: _onCancel,
          ),
          Expanded(child: _buildSearchResults()),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isSearching) {
      return const Center(
        child: LoadingIndicator(message: '搜索中...'),
      );
    }

    if (_currentQuery.isEmpty) {
      return _buildEmptyState();
    }

    if (_filteredKnowledgeBases.isEmpty && _filteredNotes.isEmpty) {
      return _buildNoResultsState();
    }

    return SearchResultsList(
      knowledgeBases: _filteredKnowledgeBases,
      notes: _filteredNotes,
      onKnowledgeBaseTap: _onKnowledgeBaseTap,
      onNoteTap: _onNoteTap,
    );
  }

  Widget _buildEmptyState() {
    return const EmptyState(
      icon: Icons.search,
      title: '输入关键词开始搜索',
      subtitle: '搜索知识库、笔记内容',
    );
  }

  Widget _buildNoResultsState() {
    return const EmptyState(
      icon: Icons.search_off,
      title: '未找到相关结果',
      subtitle: '尝试使用其他关键词',
    );
  }


}

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  bool _isLoggedIn = false;
  String? _userEmail;
  String? _userNickname;

  // 检查是否已登录
  bool get isLoggedIn => _isLoggedIn;
  
  // 获取用户信息
  String? get userEmail => _userEmail;
  String? get userNickname => _userNickname;

  // 登录
  Future<bool> login(String email, String password) async {
    try {
      // TODO: 实现实际的登录API调用
      await Future.delayed(const Duration(seconds: 2)); // 模拟网络请求
      
      // 模拟登录成功
      _isLoggedIn = true;
      _userEmail = email;
      _userNickname = email.split('@')[0]; // 简单地使用邮箱前缀作为昵称
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Google登录
  Future<bool> loginWithGoogle() async {
    try {
      // TODO: 实现Google登录
      await Future.delayed(const Duration(seconds: 2));
      
      // 模拟登录成功
      _isLoggedIn = true;
      _userEmail = '<EMAIL>';
      _userNickname = 'Google用户';
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // 注册
  Future<bool> register(String nickname, String email, String password) async {
    try {
      // TODO: 实现实际的注册API调用
      await Future.delayed(const Duration(seconds: 2)); // 模拟网络请求
      
      // 模拟注册成功
      return true;
    } catch (e) {
      return false;
    }
  }

  // 登出
  void logout() {
    _isLoggedIn = false;
    _userEmail = null;
    _userNickname = null;
  }
}

import '../models/knowledge_base.dart';
import '../models/note.dart';
import '../models/user.dart';

class MockDataService {
  static int _refreshCount = 0;

  static List<KnowledgeBase> getKnowledgeBases() {
    _refreshCount++;
    final baseKnowledgeBases = _getBaseKnowledgeBases();

    // 模拟数据更新：每次刷新时稍微改变一些数据
    return baseKnowledgeBases.map((kb) {
      return KnowledgeBase(
        id: kb.id,
        title: kb.title,
        contentCount: kb.contentCount + _refreshCount,
        userCount: kb.userCount + (_refreshCount * 10),
        author: kb.author,
        authorAvatar: kb.authorAvatar,
        description: kb.description,
        createdAt: kb.createdAt,
        coverUrl: kb.coverUrl,
        isPublic: kb.isPublic,
        permission: kb.permission,
      );
    }).toList();
  }

  static List<KnowledgeBase> _getBaseKnowledgeBases() {
    return [
      KnowledgeBase(
        id: '1',
        title: 'Flutter开发指南',
        contentCount: 156,
        userCount: 2340,
        author: 'VideoSeek',
        authorAvatar: '',
        description: '全面的Flutter开发教程和最佳实践',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        coverUrl: 'https://picsum.photos/200/300?random=1',
        isPublic: true,
        permission: KnowledgeBasePermission.public,
      ),
      KnowledgeBase(
        id: '2',
        title: 'UI/UX设计精髓',
        contentCount: 89,
        userCount: 1876,
        author: 'DataTool',
        authorAvatar: '',
        description: '现代移动应用UI/UX设计原则和技巧',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        coverUrl: 'https://picsum.photos/200/300?random=2',
        isPublic: false,
        permission: KnowledgeBasePermission.team,
      ),
      KnowledgeBase(
        id: '3',
        title: '数据结构与算法',
        contentCount: 234,
        userCount: 3456,
        author: 'DataTool',
        authorAvatar: '',
        description: '计算机科学基础知识详解',
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        coverUrl: 'https://picsum.photos/200/300?random=3',
        isPublic: true,
        permission: KnowledgeBasePermission.public,
      ),
      KnowledgeBase(
        id: '4',
        title: '移动端性能优化',
        contentCount: 67,
        userCount: 987,
        author: 'VideoSeek',
        authorAvatar: '',
        description: 'iOS和Android应用性能优化技巧',
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        coverUrl: 'https://picsum.photos/200/300?random=4',
        isPublic: false,
        permission: KnowledgeBasePermission.private,
      ),
    ];
  }

  static List<Note> getNotes() {
    final baseNotes = _getBaseNotes();

    // 模拟数据更新：每次刷新时更新点赞和评论数
    return baseNotes.map((note) {
      return Note(
        id: note.id,
        title: note.title,
        content: note.content,
        author: note.author,
        authorAvatar: note.authorAvatar,
        images: note.images,
        tags: note.tags,
        likeCount: note.likeCount + _refreshCount,
        commentCount: note.commentCount + (_refreshCount ~/ 2),
        createdAt: note.createdAt,
        updatedAt: note.updatedAt,
      );
    }).toList();
  }

  static List<Note> _getBaseNotes() {
    return [
      Note(
        id: '1',
        title: 'Flutter状态管理最佳实践',
        content: '在Flutter开发中，状态管理是一个核心概念。本文将介绍几种主流的状态管理方案...',
        author: '小明',
        authorAvatar: '',
        images: [],
        tags: ['Flutter', '状态管理', '开发'],
        likeCount: 234,
        commentCount: 45,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
      Note(
        id: '2',
        title: '设计系统的重要性',
        content: '一个好的设计系统可以大大提高团队的工作效率，保证产品的一致性...',
        author: '小红',
        authorAvatar: '',
        images: [],
        tags: ['设计', 'UI', '团队协作'],
        likeCount: 156,
        commentCount: 23,
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 4)),
      ),
      Note(
        id: '3',
        title: '移动端适配技巧',
        content: '在移动端开发中，适配不同屏幕尺寸是一个重要的挑战。这里分享一些实用的技巧...',
        author: '小李',
        authorAvatar: '',
        images: [],
        tags: ['移动端', '适配', '响应式'],
        likeCount: 89,
        commentCount: 12,
        createdAt: DateTime.now().subtract(const Duration(hours: 8)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 7)),
      ),
      Note(
        id: '4',
        title: 'API设计原则',
        content: '良好的API设计是后端开发的基础，本文总结了一些重要的设计原则...',
        author: '小张',
        authorAvatar: '',
        images: [],
        tags: ['API', '后端', '设计'],
        likeCount: 67,
        commentCount: 8,
        createdAt: DateTime.now().subtract(const Duration(hours: 12)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 11)),
      ),
      Note(
        id: '5',
        title: '代码重构的艺术',
        content: '重构是软件开发中的重要环节，如何优雅地重构代码是每个开发者都应该掌握的技能...',
        author: '小王',
        authorAvatar: '',
        images: [],
        tags: ['重构', '代码质量', '最佳实践'],
        likeCount: 145,
        commentCount: 34,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 20)),
      ),
    ];
  }

  static User getCurrentUser() {
    return User(
      id: 'current_user',
      name: '当前用户',
      email: '<EMAIL>',
      avatar: '',
      bio: '热爱学习的开发者',
      followersCount: 123,
      followingCount: 456,
      joinedAt: DateTime.now().subtract(const Duration(days: 365)),
    );
  }

  // 获取我创建的知识库
  static List<KnowledgeBase> getMyKnowledgeBases() {
    return [
      KnowledgeBase(
        id: 'my_1',
        title: 'Flutter开发指南',
        description: 'Flutter移动应用开发的完整指南',
        coverUrl: 'https://picsum.photos/200/300?random=1',
        isPublic: false,
        contentCount: 25,
        userCount: 1,
        permission: KnowledgeBasePermission.private,
        author: '我',
        authorAvatar: '',
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
      KnowledgeBase(
        id: 'my_2',
        title: 'UI设计规范',
        description: '移动应用UI设计的最佳实践',
        coverUrl: 'https://picsum.photos/200/300?random=2',
        isPublic: true,
        contentCount: 18,
        userCount: 156,
        permission: KnowledgeBasePermission.public,
        author: '我',
        authorAvatar: '',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
      KnowledgeBase(
        id: 'my_3',
        title: '团队协作手册',
        description: '高效团队协作的方法与工具',
        coverUrl: 'https://picsum.photos/200/300?random=6',
        isPublic: false,
        contentCount: 32,
        userCount: 8,
        permission: KnowledgeBasePermission.team,
        author: '我',
        authorAvatar: '',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ];
  }

  // 获取我订阅的知识库
  static List<KnowledgeBase> getSubscribedKnowledgeBases() {
    return [
      KnowledgeBase(
        id: 'sub_1',
        title: 'React Native实战',
        description: '跨平台移动应用开发',
        coverUrl: 'https://picsum.photos/200/300?random=3',
        isPublic: true,
        contentCount: 42,
        userCount: 289,
        permission: KnowledgeBasePermission.public,
        author: 'React专家',
        authorAvatar: '',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      KnowledgeBase(
        id: 'sub_2',
        title: 'Vue.js进阶教程',
        description: '深入学习Vue.js框架',
        coverUrl: 'https://picsum.photos/200/300?random=7',
        isPublic: true,
        contentCount: 56,
        userCount: 423,
        permission: KnowledgeBasePermission.public,
        author: 'Vue大师',
        authorAvatar: '',
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
      ),
    ];
  }

  // 获取知识广场的知识库
  static List<KnowledgeBase> getPublicKnowledgeBases() {
    return [
      KnowledgeBase(
        id: 'pub_1',
        title: '人工智能基础',
        description: 'AI技术入门与实践',
        coverUrl: 'https://picsum.photos/200/300?random=4',
        isPublic: true,
        contentCount: 67,
        userCount: 1024,
        permission: KnowledgeBasePermission.public,
        author: 'AI研究院',
        authorAvatar: '',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
      ),
      KnowledgeBase(
        id: 'pub_2',
        title: '数据科学导论',
        description: '数据分析与机器学习',
        coverUrl: 'https://picsum.photos/200/300?random=5',
        isPublic: true,
        contentCount: 89,
        userCount: 756,
        permission: KnowledgeBasePermission.public,
        author: '数据科学家',
        authorAvatar: '',
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
      ),
      KnowledgeBase(
        id: 'pub_3',
        title: '区块链技术详解',
        description: '从入门到精通的区块链学习路径',
        coverUrl: 'https://picsum.photos/200/300?random=8',
        isPublic: true,
        contentCount: 134,
        userCount: 892,
        permission: KnowledgeBasePermission.public,
        author: '区块链专家',
        authorAvatar: '',
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
      ),
      KnowledgeBase(
        id: 'pub_4',
        title: '云计算架构设计',
        description: '现代云原生应用架构设计指南',
        coverUrl: 'https://picsum.photos/200/300?random=9',
        isPublic: true,
        contentCount: 78,
        userCount: 567,
        permission: KnowledgeBasePermission.public,
        author: '云架构师',
        authorAvatar: '',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
    ];
  }
}

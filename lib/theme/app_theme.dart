import 'package:flutter/material.dart';

class AppTheme {
  // ========== 现代化AI科技感配色 ==========
  static const Color primaryColor = Color(0xFF4F46E5);      // 现代靛蓝色，AI科技感
  static const Color secondaryColor = Color(0xFF7C3AED);    // 深紫色，科技感
  static const Color accentColor = Color(0xFF06B6D4);       // 青色，现代点缀
  static const Color gradientStart = Color(0xFF4F46E5);     // 渐变起始色
  static const Color gradientEnd = Color(0xFF06B6D4);       // 渐变结束色
  static const Color successColor = Color(0xFF059669);      // 现代绿色
  static const Color warningColor = Color(0xFFD97706);      // 现代橙色
  static const Color errorColor = Color(0xFFDC2626);        // 现代红色

  // ========== 白天模式 现代AI科技感配色 ==========
  // 背景色系 - 现代明亮科技感
  static const Color lightBackgroundColor = Color(0xFFFAFBFC);   // 极淡现代白色背景
  static const Color lightSurfaceColor = Color(0xFFFFFFFF);      // 纯白色表面
  static const Color lightCardColor = Color(0xFFFFFFFF);         // 纯白卡片，现代感

  // 文字颜色 - 现代深色文字，清晰对比
  static const Color lightTextPrimaryColor = Color(0xFF111827);  // 现代深灰色主文字
  static const Color lightTextSecondaryColor = Color(0xFF6B7280); // 现代中灰色次要文字
  static const Color lightTextTertiaryColor = Color(0xFF9CA3AF); // 现代浅灰色三级文字

  // 边框和分割线 - 现代淡雅科技感
  static const Color lightBorderColor = Color(0xFFE5E7EB);       // 现代淡灰色边框
  static const Color lightDividerColor = Color(0xFFF3F4F6);      // 现代极淡分割线
  static const Color lightShadowColor = Color(0x08000000);       // 现代轻微阴影

  // 搜索框专用颜色 - 现代明亮输入
  static const Color lightSearchBoxColor = Color(0xFFF9FAFB);    // 现代极淡背景色
  static const Color lightSearchBoxShadow = Color(0x04000000);   // 现代极轻阴影

  // ========== 夜间模式 现代AI科技感配色 ==========
  // 背景色系 - 现代深色科技感
  static const Color darkBackgroundColor = Color(0xFF0B0F1A);    // 现代深蓝黑色背景
  static const Color darkSurfaceColor = Color(0xFF1F2937);       // 现代深灰色表面
  static const Color darkCardColor = Color(0xFF374151);          // 现代卡片深灰色

  // 文字颜色 - 现代高对比度科技感
  static const Color darkTextPrimaryColor = Color(0xFFF9FAFB);   // 现代亮白色主文字
  static const Color darkTextSecondaryColor = Color(0xFFD1D5DB); // 现代中灰色次要文字
  static const Color darkTextTertiaryColor = Color(0xFF9CA3AF);  // 现代深灰色三级文字

  // 边框和分割线 - 现代科技感光效
  static const Color darkBorderColor = Color(0xFF4B5563);        // 现代灰色边框
  static const Color darkDividerColor = Color(0xFF374151);       // 现代深色分割线
  static const Color darkShadowColor = Color(0x50000000);        // 现代深色阴影

  // 搜索框专用颜色 - 现代科技感输入
  static const Color darkSearchBoxColor = Color(0xFF1F2937);     // 现代深色搜索框背景
  static const Color darkSearchBoxShadow = Color(0x70000000);    // 现代深色阴影

  // ========== 白天模式主题 ==========
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
        surface: lightSurfaceColor,
      ),
      scaffoldBackgroundColor: lightBackgroundColor,
      cardTheme: CardThemeData(
        color: lightCardColor,
        elevation: 1,                    // 现代化极轻阴影
        shadowColor: lightShadowColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20), // 更大圆角，现代感
          side: BorderSide(
            color: lightBorderColor,
            width: 0.3,                  // 极细边框，现代简洁
          ),
        ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: lightBackgroundColor,
        elevation: 0,
        scrolledUnderElevation: 0,
        titleTextStyle: TextStyle(
          color: lightTextPrimaryColor,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
        iconTheme: IconThemeData(
          color: lightTextPrimaryColor,
        ),
      ),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          color: lightTextPrimaryColor,
          fontSize: 24,
          fontWeight: FontWeight.w600,
          letterSpacing: -0.3,
          height: 1.3,
        ),
        headlineMedium: TextStyle(
          color: lightTextPrimaryColor,
          fontSize: 20,
          fontWeight: FontWeight.w500,
          letterSpacing: -0.2,
          height: 1.3,
        ),
        titleLarge: TextStyle(
          color: lightTextPrimaryColor,
          fontSize: 18,
          fontWeight: FontWeight.w500,
          letterSpacing: -0.1,
          height: 1.3,
        ),
        titleMedium: TextStyle(
          color: lightTextPrimaryColor,
          fontSize: 16,
          fontWeight: FontWeight.w500,
          letterSpacing: 0,
          height: 1.3,
        ),
        bodyLarge: TextStyle(
          color: lightTextPrimaryColor,
          fontSize: 16,
          fontWeight: FontWeight.w400,
          height: 1.5,
        ),
        bodyMedium: TextStyle(
          color: lightTextSecondaryColor,
          fontSize: 14,
          fontWeight: FontWeight.w400,
          height: 1.4,
        ),
        bodySmall: TextStyle(
          color: lightTextTertiaryColor,
          fontSize: 12,
          fontWeight: FontWeight.w400,
          height: 1.3,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: lightSearchBoxColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),  // 更大圆角，现代感
          borderSide: BorderSide.none,              // 无边框，现代简洁
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        hintStyle: const TextStyle(
          color: lightTextTertiaryColor,
          fontWeight: FontWeight.w400,
        ),
        labelStyle: const TextStyle(
          color: lightTextSecondaryColor,
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 18), // 更大内边距
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: primaryColor,
        linearTrackColor: lightDividerColor,
        circularTrackColor: lightDividerColor,
      ),

      // 现代化白天模式按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,                      // 现代扁平化设计
          shadowColor: Colors.transparent,   // 无阴影，现代感
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24), // 更大圆角，现代感
          ),
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16), // 更大内边距
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
      ),

      dividerTheme: const DividerThemeData(
        color: lightDividerColor,
        thickness: 0.5,
        space: 1,
      ),
    );
  }

  // ========== 夜间模式主题 ==========
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.dark,
        surface: darkSurfaceColor,
      ),
      scaffoldBackgroundColor: darkBackgroundColor,
      cardTheme: CardThemeData(
        color: darkCardColor,
        elevation: 2,                    // 现代化适中阴影
        shadowColor: darkShadowColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20), // 更大圆角，现代感
          side: BorderSide(
            color: darkBorderColor,
            width: 0.5,                  // 更细边框，现代感
          ),
        ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: darkBackgroundColor,
        elevation: 0,
        scrolledUnderElevation: 0,
        titleTextStyle: TextStyle(
          color: darkTextPrimaryColor,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
        iconTheme: IconThemeData(
          color: darkTextPrimaryColor,
        ),
      ),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          color: darkTextPrimaryColor,
          fontSize: 24,
          fontWeight: FontWeight.w500,
          letterSpacing: -0.3,
          height: 1.3,
        ),
        headlineMedium: TextStyle(
          color: darkTextPrimaryColor,
          fontSize: 20,
          fontWeight: FontWeight.w400,
          letterSpacing: -0.2,
          height: 1.3,
        ),
        titleLarge: TextStyle(
          color: darkTextPrimaryColor,
          fontSize: 18,
          fontWeight: FontWeight.w400,
          letterSpacing: -0.1,
          height: 1.3,
        ),
        titleMedium: TextStyle(
          color: darkTextPrimaryColor,
          fontSize: 16,
          fontWeight: FontWeight.w400,
          letterSpacing: 0,
          height: 1.3,
        ),
        bodyLarge: TextStyle(
          color: darkTextPrimaryColor,
          fontSize: 16,
          fontWeight: FontWeight.w300,
          height: 1.6,
        ),
        bodyMedium: TextStyle(
          color: darkTextSecondaryColor,
          fontSize: 14,
          fontWeight: FontWeight.w300,
          height: 1.5,
        ),
        bodySmall: TextStyle(
          color: darkTextTertiaryColor,
          fontSize: 12,
          fontWeight: FontWeight.w300,
          height: 1.4,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkSearchBoxColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),  // 更大圆角，现代感
          borderSide: BorderSide.none,              // 无边框，现代简洁
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        hintStyle: const TextStyle(
          color: darkTextTertiaryColor,
          fontWeight: FontWeight.w400,
        ),
        labelStyle: const TextStyle(
          color: darkTextSecondaryColor,
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 18), // 更大内边距
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: primaryColor,
        linearTrackColor: darkDividerColor,
        circularTrackColor: darkDividerColor,
      ),

      // 现代化夜间模式按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,                      // 现代扁平化设计
          shadowColor: Colors.transparent,   // 无阴影，现代感
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24), // 更大圆角，现代感
          ),
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16), // 更大内边距
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
      ),

      dividerTheme: const DividerThemeData(
        color: darkDividerColor,
        thickness: 0.3,
        space: 0.5,
      ),
    );
  }

  // ========== 便捷访问器方法 ==========
  // 根据当前主题模式获取对应的颜色
  static Color getBackgroundColor(bool isDark) =>
      isDark ? darkBackgroundColor : lightBackgroundColor;

  static Color getSurfaceColor(bool isDark) =>
      isDark ? darkSurfaceColor : lightSurfaceColor;

  static Color getCardColor(bool isDark) =>
      isDark ? darkCardColor : lightCardColor;

  static Color getTextPrimaryColor(bool isDark) =>
      isDark ? darkTextPrimaryColor : lightTextPrimaryColor;

  static Color getTextSecondaryColor(bool isDark) =>
      isDark ? darkTextSecondaryColor : lightTextSecondaryColor;

  static Color getTextTertiaryColor(bool isDark) =>
      isDark ? darkTextTertiaryColor : lightTextTertiaryColor;

  static Color getBorderColor(bool isDark) =>
      isDark ? darkBorderColor : lightBorderColor;

  static Color getDividerColor(bool isDark) =>
      isDark ? darkDividerColor : lightDividerColor;

  static Color getShadowColor(bool isDark) =>
      isDark ? darkShadowColor : lightShadowColor;

  static Color getSearchBoxColor(bool isDark) =>
      isDark ? darkSearchBoxColor : lightSearchBoxColor;

  static Color getSearchBoxShadow(bool isDark) =>
      isDark ? darkSearchBoxShadow : lightSearchBoxShadow;

  // ========== 向后兼容的静态属性 ==========
  // 默认使用白天模式的颜色，保持向后兼容
  static const Color backgroundColor = lightBackgroundColor;
  static const Color surfaceColor = lightSurfaceColor;
  static const Color cardColor = lightCardColor;
  static const Color textPrimaryColor = lightTextPrimaryColor;
  static const Color textSecondaryColor = lightTextSecondaryColor;
  static const Color textTertiaryColor = lightTextTertiaryColor;
  static const Color borderColor = lightBorderColor;
  static const Color dividerColor = lightDividerColor;
  static const Color shadowColor = lightShadowColor;
  static const Color searchBoxColor = lightSearchBoxColor;
  static const Color searchBoxShadow = lightSearchBoxShadow;
}

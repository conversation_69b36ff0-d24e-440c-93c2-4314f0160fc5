/// 时间工具类
/// 提供各种时间格式化和处理方法
class TimeUtils {
  /// 格式化相对时间
  /// 将时间转换为相对于当前时间的描述（如：刚刚、5分钟前、2小时前、3天前）
  static String formatRelativeTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 格式化创建时间
  /// 专门用于显示创建时间，带有"创建于"前缀
  static String formatCreateTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '创建于$months个月前';
    } else if (difference.inDays > 0) {
      return '创建于${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '创建于${difference.inHours}小时前';
    } else {
      return '刚刚创建';
    }
  }

  /// 格式化发布时间
  /// 专门用于显示发布时间，更详细的时间描述
  static String formatPublishTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 7) {
      // 超过7天显示具体日期
      return '${time.month}月${time.day}日';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚发布';
    }
  }

  /// 格式化更新时间
  /// 专门用于显示最后更新时间
  static String formatUpdateTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前更新';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前更新';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前更新';
    } else {
      return '刚刚更新';
    }
  }

  /// 格式化完整日期时间
  /// 返回格式：2024年1月15日 14:30
  static String formatFullDateTime(DateTime time) {
    return '${time.year}年${time.month}月${time.day}日 ${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化日期
  /// 返回格式：2024年1月15日
  static String formatDate(DateTime time) {
    return '${time.year}年${time.month}月${time.day}日';
  }

  /// 格式化时间
  /// 返回格式：14:30
  static String formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// 判断是否是今天
  static bool isToday(DateTime time) {
    final now = DateTime.now();
    return time.year == now.year && 
           time.month == now.month && 
           time.day == now.day;
  }

  /// 判断是否是昨天
  static bool isYesterday(DateTime time) {
    final now = DateTime.now();
    final yesterday = now.subtract(const Duration(days: 1));
    return time.year == yesterday.year && 
           time.month == yesterday.month && 
           time.day == yesterday.day;
  }

  /// 智能格式化时间
  /// 根据时间距离现在的长短，自动选择最合适的显示格式
  static String formatSmartTime(DateTime time) {
    if (isToday(time)) {
      return formatTime(time);
    } else if (isYesterday(time)) {
      return '昨天 ${formatTime(time)}';
    } else {
      final now = DateTime.now();
      final difference = now.difference(time);
      
      if (difference.inDays < 7) {
        return formatRelativeTime(time);
      } else if (time.year == now.year) {
        return '${time.month}月${time.day}日';
      } else {
        return '${time.year}年${time.month}月${time.day}日';
      }
    }
  }
}

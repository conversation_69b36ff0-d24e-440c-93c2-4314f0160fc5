# 通用组件结构说明

本目录包含所有跨模块复用的通用组件，按功能进行分类组织。

## 📁 目录结构

```
lib/widgets/common/
├── ui/                     # 基础UI组件
│   ├── loading_indicator.dart      # 加载指示器
│   ├── empty_state.dart           # 空状态组件
│   ├── more_button.dart           # 更多操作按钮
│   └── index.dart                 # 导出文件
├── dialogs/               # 对话框组件
│   ├── create_folder_dialog.dart  # 创建文件夹对话框
│   └── index.dart                 # 导出文件
├── bottom_sheets/         # 底部弹窗组件
│   ├── note_creation/     # 笔记创建相关
│   │   ├── note_creation_options_bottom_sheet.dart
│   │   ├── paste_link_form_bottom_sheet.dart
│   │   ├── photo_note_form_bottom_sheet.dart
│   │   └── index.dart
│   ├── note_operations/   # 笔记操作相关
│   │   ├── note_operations_bottom_sheet.dart
│   │   └── index.dart
│   ├── knowledge_base/    # 知识库相关
│   │   ├── knowledge_base_selector_bottom_sheet.dart
│   │   └── index.dart
│   ├── processing/        # 处理进度相关
│   │   ├── link_processing_bottom_sheet.dart
│   │   └── index.dart
│   └── index.dart         # 总导出文件
└── index.dart             # 根导出文件
```

## 🎯 设计原则

### 1. 功能分类
- **ui/**: 基础的UI组件，如加载指示器、空状态等
- **dialogs/**: 对话框类型的组件
- **bottom_sheets/**: 底部弹窗类型的组件，按业务功能进一步分类

### 2. 业务分组
底部弹窗按业务功能分组：
- **note_creation/**: 笔记创建相关的所有弹窗
- **note_operations/**: 笔记操作相关的弹窗
- **knowledge_base/**: 知识库相关的弹窗
- **processing/**: 处理进度相关的弹窗

### 3. 导入便利性
每个子目录都有 `index.dart` 文件，方便统一导入：

```dart
// 导入所有通用组件
import 'package:videoseek/widgets/common/index.dart';

// 导入特定分类的组件
import 'package:videoseek/widgets/common/ui/index.dart';
import 'package:videoseek/widgets/common/bottom_sheets/note_creation/index.dart';

// 导入单个组件
import 'package:videoseek/widgets/common/ui/loading_indicator.dart';
```

## 🔧 使用指南

### 基础UI组件
```dart
// 加载指示器
LoadingIndicator(message: '加载中...')

// 空状态
EmptyState(
  icon: Icons.inbox,
  title: '暂无数据',
  subtitle: '点击刷新重新加载',
)

// 更多按钮
MoreButton(onTap: () => showMenu())
```

### 对话框组件
```dart
// 创建文件夹对话框
CreateFolderDialog.show(
  context,
  title: '新建文件夹',
  hint: '文件夹名称',
  onConfirm: (name) => createFolder(name),
)
```

### 底部弹窗组件
```dart
// 笔记创建选项
NoteCreationOptionsBottomSheet.show(context)

// 笔记操作
NoteOperationsBottomSheet.show(context, note)

// 知识库选择
KnowledgeBaseSelectorBottomSheet.show(context, note)
```

## 📋 维护指南

### 添加新组件
1. 确定组件类型（UI、对话框、底部弹窗）
2. 选择合适的子目录或创建新的业务分组
3. 添加组件文件
4. 更新对应的 `index.dart` 文件
5. 更新根目录的 `index.dart` 文件

### 重构现有组件
1. 移动文件到新位置
2. 更新所有引用该组件的导入路径
3. 更新相关的 `index.dart` 文件
4. 运行 `flutter analyze` 确保无错误

## 🎨 命名规范

- 文件名：使用下划线分隔的小写字母
- 类名：使用驼峰命名法
- 目录名：使用下划线分隔的小写字母
- 导出文件：统一命名为 `index.dart`

## 🚀 优势

1. **清晰的组织结构**：按功能和类型分类，便于查找和维护
2. **便利的导入方式**：通过 index.dart 文件简化导入
3. **良好的扩展性**：新组件可以轻松添加到合适的分类中
4. **减少耦合**：相关组件聚合在一起，便于统一管理
5. **提高开发效率**：开发者可以快速定位和使用所需组件

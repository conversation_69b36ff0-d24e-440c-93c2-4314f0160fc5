import 'package:flutter/material.dart';
import 'package:videoseek/theme/app_theme.dart';
import 'package:videoseek/widgets/common/bottom_sheets/note_creation/photo_note_form_bottom_sheet.dart';
import 'package:videoseek/widgets/common/bottom_sheets/note_creation/paste_link_form_bottom_sheet.dart';

// 弹窗高度配置类
class _HeightConfig {
  final double initial;
  final double min;
  final double max;

  const _HeightConfig({
    required this.initial,
    required this.min,
    required this.max,
  });
}

class NoteCreationOptionsBottomSheet extends StatefulWidget {
  final bool showQuickRecord; // 是否显示快速记录板块
  final ScrollController? scrollController; // 滚动控制器

  const NoteCreationOptionsBottomSheet({
    super.key,
    this.showQuickRecord = true,
    this.scrollController,
  });

  static void show(BuildContext context, {bool showQuickRecord = true}) {
    // 根据内容计算合适的高度范围
    final heightConfig = _calculateHeightConfig(context, showQuickRecord);

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: heightConfig.initial,
            minChildSize: heightConfig.min,
            maxChildSize: heightConfig.max,
            builder:
                (context, scrollController) => NoteCreationOptionsBottomSheet(
                  showQuickRecord: showQuickRecord,
                  scrollController: scrollController,
                ),
          ),
    );
  }

  // 根据屏幕尺寸和内容计算合适的高度配置
  static _HeightConfig _calculateHeightConfig(
    BuildContext context,
    bool showQuickRecord,
  ) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    // 估算内容高度
    double contentHeight = 0;

    // 拖拽手柄 + 顶部间距
    contentHeight += 40;

    if (showQuickRecord) {
      // 快速记录标题 + 间距 + 2个选项 + 分隔线 + 间距
      contentHeight += 40 + 12 + (2 * 80) + 16 + 16 + 16;
    }

    // 更多方式标题 + 间距 + 5个选项
    contentHeight += 40 + 12 + (5 * 80);

    // 底部间距
    contentHeight += 32;

    // 计算内容占屏幕的比例
    double contentRatio = contentHeight / screenHeight;

    // 根据屏幕尺寸调整参数
    bool isLargeScreen = screenHeight > 800; // 大屏判断
    bool isTablet = screenWidth > 600; // 平板判断

    double minSize, initialSize, maxSize;

    if (showQuickRecord) {
      // 有快速记录时的配置
      minSize = isLargeScreen ? 0.2 : 0.25;
      initialSize = contentRatio.clamp(
        isLargeScreen ? 0.4 : 0.5,
        isLargeScreen ? 0.6 : 0.75,
      );
      maxSize = isTablet ? 0.7 : (isLargeScreen ? 0.75 : 0.85);
    } else {
      // 只有更多方式时的配置
      minSize = isLargeScreen ? 0.25 : 0.3;
      initialSize = contentRatio.clamp(
        isLargeScreen ? 0.35 : 0.45,
        isLargeScreen ? 0.55 : 0.65,
      );
      maxSize = isTablet ? 0.6 : (isLargeScreen ? 0.7 : 0.8);
    }

    return _HeightConfig(initial: initialSize, min: minSize, max: maxSize);
  }

  @override
  State<NoteCreationOptionsBottomSheet> createState() =>
      _NoteCreationOptionsBottomSheetState();
}

class _NoteCreationOptionsBottomSheetState
    extends State<NoteCreationOptionsBottomSheet> {
  final TextEditingController _textController = TextEditingController();

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: SingleChildScrollView(
        controller: widget.scrollController,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHandle(),
            if (widget.showQuickRecord) ...[
              _buildQuickRecord(context),
              const SizedBox(height: 16),
              const Divider(color: AppTheme.borderColor),
              const SizedBox(height: 16),
            ],
            _buildMoreOptions(context),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppTheme.borderColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildQuickRecord(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '快速记录',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),

          // 快速记录选项
          _buildOptionItem(
            context,
            icon: Icons.mic,
            title: '点击开始录音',
            subtitle: '语音转文字，快速记录想法',
            onTap: _openRecordingBottomSheet,
          ),
          _buildOptionItem(
            context,
            icon: Icons.edit,
            title: '输入或粘贴文字',
            subtitle: '手动输入文字内容',
            onTap: _openTextInputPage,
          ),
        ],
      ),
    );
  }

  Widget _buildMoreOptions(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '更多方式',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),

          // 选项列表
          _buildOptionItem(
            context,
            icon: Icons.link,
            title: '粘贴链接',
            subtitle: '从网页链接生成笔记',
            onTap: () => _onPasteLink(context),
          ),
          _buildOptionItem(
            context,
            icon: Icons.camera_alt,
            title: '拍照',
            subtitle: '随手一拍，瞬间提取文字与重点',
            onTap: () => _onTakePhoto(context),
          ),
          _buildOptionItem(
            context,
            icon: Icons.photo_library,
            title: '上传图片',
            subtitle: '课堂笔记、白板记录、旅行风景等',
            onTap: () => _showToast(context, '上传图片'),
          ),
          _buildOptionItem(
            context,
            icon: Icons.record_voice_over,
            title: '线下会议录音',
            subtitle: '时长1小时起，区分发言人高效记录',
            onTap: () => _showToast(context, '线下会议录音'),
          ),
          _buildOptionItem(
            context,
            icon: Icons.video_library,
            title: '导入音视频',
            subtitle: '自动转文字稿，AI智能总结',
            onTap: () => _showToast(context, '导入音视频'),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(vertical: 4),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: AppTheme.primaryColor, size: 24),
        ),
        title: Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: AppTheme.textSecondaryColor),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppTheme.textSecondaryColor,
        ),
        onTap: () {
          Navigator.of(context).pop();
          onTap();
        },
      ),
    );
  }

  void _openRecordingBottomSheet() {
    Navigator.of(context).pop();
    _showToast(context, '打开录音底部弹窗');
  }

  void _openTextInputPage() {
    Navigator.of(context).pop();
    _showToast(context, '打开文字输入页面');
  }

  void _onPasteLink(BuildContext context) {
    // 不关闭当前弹窗，直接显示粘贴链接表单弹窗
    PasteLinkFormBottomSheet.show(context);
  }

  void _onTakePhoto(BuildContext context) {
    // 不关闭当前弹窗，直接显示拍照表单弹窗
    PhotoNoteFormBottomSheet.show(context);
  }

  void _showToast(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('测试: $message'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:videoseek/models/note.dart';
import 'package:videoseek/theme/app_theme.dart';
import 'package:videoseek/utils/time_utils.dart';
import 'package:videoseek/widgets/common/bottom_sheets/knowledge_base/knowledge_base_selector_bottom_sheet.dart';
import 'package:videoseek/widgets/common/bottom_sheets/note_creation/note_creation_options_bottom_sheet.dart';

class NoteOperationsBottomSheet extends StatelessWidget {
  final Note note;
  final String? knowledgeBaseName; // 关联的知识库名称，null表示未关联

  const NoteOperationsBottomSheet({
    super.key,
    required this.note,
    this.knowledgeBaseName,
  });

  static void show(
    BuildContext context,
    Note note, {
    String? knowledgeBaseName,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.6,
            minChildSize: 0.4,
            maxChildSize: 0.8,
            builder:
                (context, scrollController) => NoteOperationsBottomSheet(
                  note: note,
                  knowledgeBaseName: knowledgeBaseName,
                ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _buildHandle(),
          _buildHeader(context),
          _buildActionButtons(context),
          const Divider(color: AppTheme.borderColor, height: 1),
          Expanded(child: _buildActionList(context)),
        ],
      ),
    );
  }

  Widget _buildHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppTheme.borderColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 笔记类型图标
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              _getNoteTypeIcon(),
              color: AppTheme.primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          // 笔记信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  note.title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  '字数：${_getWordCount()} · 更新时间：${TimeUtils.formatUpdateTime(note.createdAt)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildActionButton(
              context,
              icon: Icons.edit,
              label: '编辑',
              onTap: () => _onEdit(context),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              context,
              icon: Icons.share,
              label: '分享',
              onTap: () => _onShare(context),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              context,
              icon: Icons.add,
              label: '追加笔记',
              onTap: () => _onAppend(context),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              context,
              icon: Icons.download,
              label: '导出',
              onTap: () => _onExport(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppTheme.borderColor, width: 1),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: AppTheme.textPrimaryColor, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textPrimaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionList(BuildContext context) {
    final actions = [
      _ActionItem(
        icon: Icons.copy,
        title: '复制笔记',
        subtitle: '复制笔记内容到剪贴板',
        onTap: () => _onCopy(context),
      ),
      _ActionItem(
        icon:
            knowledgeBaseName != null
                ? Icons.folder_open
                : Icons.folder_outlined,
        title: knowledgeBaseName != null ? '移出知识库' : '添加到知识库',
        subtitle: knowledgeBaseName != null ? '从知识库中移除此笔记' : '将笔记添加到指定知识库',
        trailing:
            knowledgeBaseName != null
                ? Text(
                  knowledgeBaseName!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                )
                : null,
        onTap: () => _onKnowledgeBaseAction(context),
      ),
      _ActionItem(
        icon: Icons.delete,
        title: '删除',
        subtitle: '永久删除此笔记',
        isDestructive: true,
        onTap: () => _onDelete(context),
      ),
    ];

    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      itemCount: actions.length,
      separatorBuilder:
          (context, index) =>
              const Divider(height: 1, color: AppTheme.borderColor),
      itemBuilder: (context, index) {
        final action = actions[index];
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(vertical: 8),
          leading: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color:
                  action.isDestructive
                      ? Colors.red.withValues(alpha: 0.1)
                      : AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              action.icon,
              color: action.isDestructive ? Colors.red : AppTheme.primaryColor,
              size: 24,
            ),
          ),
          title: Text(
            action.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color:
                  action.isDestructive ? Colors.red : AppTheme.textPrimaryColor,
            ),
          ),
          subtitle: Text(
            action.subtitle,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppTheme.textSecondaryColor),
          ),
          trailing:
              action.trailing ??
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color:
                    action.isDestructive
                        ? Colors.red
                        : AppTheme.textSecondaryColor,
              ),
          onTap: () {
            Navigator.of(context).pop();
            action.onTap();
          },
        );
      },
    );
  }

  // 获取笔记类型图标
  IconData _getNoteTypeIcon() {
    // 这里可以根据笔记类型返回不同图标
    // 暂时使用通用的笔记图标
    return Icons.note;
  }

  // 获取字数统计
  int _getWordCount() {
    return note.content.length;
  }

  // 操作方法
  void _onEdit(BuildContext context) {
    _showToast(context, '编辑笔记');
  }

  void _onShare(BuildContext context) {
    _showToast(context, '分享笔记');
  }

  void _onAppend(BuildContext context) {
    // 关闭当前弹窗
    Navigator.of(context).pop();

    // 显示包含快速记录和更多方式的完整弹窗，与笔记详情页面保持一致
    NoteCreationOptionsBottomSheet.show(context, showQuickRecord: true);
  }

  void _onExport(BuildContext context) {
    _showToast(context, '导出笔记');
  }

  void _onCopy(BuildContext context) {
    _showToast(context, '复制笔记');
  }

  void _onKnowledgeBaseAction(BuildContext context) {
    if (knowledgeBaseName != null) {
      _showToast(context, '移出知识库');
    } else {
      // 不关闭当前弹窗，直接显示知识库选择弹窗
      KnowledgeBaseSelectorBottomSheet.show(context, note);
    }
  }

  void _onDelete(BuildContext context) {
    _showToast(context, '删除笔记');
  }

  void _showToast(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('测试: $message'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}

class _ActionItem {
  final IconData icon;
  final String title;
  final String subtitle;
  final Widget? trailing;
  final bool isDestructive;
  final VoidCallback onTap;

  const _ActionItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    this.trailing,
    this.isDestructive = false,
    required this.onTap,
  });
}

import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../models/note.dart';
import '../../theme/app_theme.dart';
import '../../pages/knowledge_base/create_knowledge_base_page.dart';
import 'empty_state.dart';
import 'create_folder_dialog.dart';

class KnowledgeBaseSelectorBottomSheet extends StatefulWidget {
  final Note note;

  const KnowledgeBaseSelectorBottomSheet({
    super.key,
    required this.note,
  });

  static void show(BuildContext context, Note note) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        builder: (context, scrollController) => KnowledgeBaseSelectorBottomSheet(
          note: note,
        ),
      ),
    );
  }

  @override
  State<KnowledgeBaseSelectorBottomSheet> createState() => _KnowledgeBaseSelectorBottomSheetState();
}

class _KnowledgeBaseSelectorBottomSheetState extends State<KnowledgeBaseSelectorBottomSheet> {
  KnowledgeBase? _selectedKnowledgeBase;
  List<Map<String, dynamic>> _folders = [];
  final List<Map<String, dynamic>> _folderPath = []; // 文件夹路径栈
  bool _isLoadingFolders = false;

  // 模拟知识库数据
  final List<KnowledgeBase> _knowledgeBases = [
    KnowledgeBase(
      id: '1',
      title: 'Flutter开发指南',
      description: 'Flutter相关的学习笔记和开发经验',
      contentCount: 25,
      userCount: 3,
      author: 'Flutter团队',
      authorAvatar: 'https://picsum.photos/200/300?random=1',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      coverUrl: 'https://picsum.photos/400/300?random=1',
    ),
    KnowledgeBase(
      id: '2',
      title: 'AI与机器学习',
      description: '人工智能和机器学习相关资料整理',
      contentCount: 42,
      userCount: 5,
      author: 'AI研究团队',
      authorAvatar: 'https://picsum.photos/200/300?random=2',
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      coverUrl: 'https://picsum.photos/400/300?random=2',
    ),
    KnowledgeBase(
      id: '3',
      title: '产品设计思考',
      description: '产品设计理念和用户体验相关内容',
      contentCount: 18,
      userCount: 2,
      author: '设计团队',
      authorAvatar: 'https://picsum.photos/200/300?random=3',
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
      coverUrl: 'https://picsum.photos/400/300?random=3',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _buildHandle(),
          _buildHeader(),
          Expanded(
            child: _selectedKnowledgeBase == null
                ? _buildKnowledgeBaseList()
                : _buildFolderList(),
          ),
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppTheme.borderColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_selectedKnowledgeBase != null)
            IconButton(
              onPressed: _goBack,
              icon: const Icon(
                Icons.arrow_back_ios,
                color: AppTheme.textPrimaryColor,
                size: 20,
              ),
            ),
          Expanded(
            child: Text(
              _getHeaderTitle(),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKnowledgeBaseList() {
    if (_knowledgeBases.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: _knowledgeBases.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final knowledgeBase = _knowledgeBases[index];
        return _buildKnowledgeBaseItem(knowledgeBase);
      },
    );
  }

  Widget _buildKnowledgeBaseItem(KnowledgeBase knowledgeBase) {
    return GestureDetector(
      onTap: () => _selectKnowledgeBase(knowledgeBase),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.borderColor,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: knowledgeBase.coverUrl != null
                  ? Image.network(
                      knowledgeBase.coverUrl!,
                      width: 48,
                      height: 48,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.folder,
                          color: AppTheme.primaryColor,
                          size: 24,
                        ),
                      ),
                    )
                  : Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.folder,
                        color: AppTheme.primaryColor,
                        size: 24,
                      ),
                    ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    knowledgeBase.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${knowledgeBase.contentCount}个内容 · ${knowledgeBase.userCount}个成员',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppTheme.textSecondaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFolderList() {
    if (_isLoadingFolders) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
        ),
      );
    }

    if (_folders.isEmpty) {
      return _buildEmptyFolderState();
    }

    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: _folders.length,
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        color: AppTheme.borderColor,
      ),
      itemBuilder: (context, index) {
        final folder = _folders[index];
        return _buildFolderItem(folder);
      },
    );
  }

  Widget _buildFolderItem(Map<String, dynamic> folder) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 8),
      leading: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: AppTheme.borderColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(
          Icons.folder,
          color: AppTheme.textSecondaryColor,
          size: 24,
        ),
      ),
      title: Text(
        folder['name'],
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: AppTheme.textPrimaryColor,
        ),
      ),
      subtitle: Text(
        '${folder['itemCount']}个项目',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppTheme.textSecondaryColor,
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppTheme.textSecondaryColor,
      ),
      onTap: () => _enterFolder(folder),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_outlined,
            size: 64,
            color: AppTheme.textSecondaryColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '暂无知识库',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '创建第一个知识库来整理您的笔记',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyFolderState() {
    return const EmptyState(
      icon: Icons.folder_copy_outlined,
      title: '文件夹及其中的内容复制到此处',
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppTheme.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 左侧按钮
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _selectedKnowledgeBase == null ? _createKnowledgeBase : _createFolder,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: const BorderSide(color: AppTheme.borderColor),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              icon: Icon(
                _selectedKnowledgeBase == null ? Icons.add : Icons.create_new_folder,
                size: 20,
                color: AppTheme.textPrimaryColor,
              ),
              label: Text(
                _selectedKnowledgeBase == null ? '新建知识库' : '新建文件夹',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ),
          ),
          
          // 右侧按钮（仅在选择了知识库时显示）
          if (_selectedKnowledgeBase != null) ...[
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton(
                onPressed: _moveToFolder, // 进入知识库后就可以点击
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  '移动到此处',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _selectKnowledgeBase(KnowledgeBase knowledgeBase) {
    setState(() {
      _selectedKnowledgeBase = knowledgeBase;
      _folderPath.clear(); // 清空文件夹路径
      _isLoadingFolders = true;
    });

    _loadFolders();
  }

  void _createKnowledgeBase() {
    // 不关闭弹窗，直接跳转到创建页面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateKnowledgeBasePage(),
      ),
    ).then((result) {
      // 如果创建成功，可以在这里处理返回结果
      if (mounted && result != null) {
        // 创建成功后关闭知识库选择弹窗
        Navigator.of(context).pop();
        // 关闭笔记操作弹窗（如果存在）
        Navigator.of(context).pop();
      }
    });
  }

  void _createFolder() {
    CreateFolderDialog.show(
      context,
      title: '新建文件夹',
      hint: '文件夹名称',
      onConfirm: _handleCreateFolder,
    );
  }

  void _handleCreateFolder(String folderName) {
    // TODO: 实现创建文件夹逻辑
    // 这里可以调用API创建文件夹，然后刷新文件夹列表
    _showToast('创建文件夹: $folderName');

    // 模拟创建成功后刷新列表
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _loadFolders();
      }
    });
  }

  void _moveToFolder() {
    // 构建目标路径
    String destination = _selectedKnowledgeBase!.title;
    if (_folderPath.isNotEmpty) {
      final pathNames = _folderPath.map((folder) => folder['name']).join(' / ');
      destination = '$destination / $pathNames';
    }

    // 关闭知识库选择弹窗
    Navigator.of(context).pop();
    // 关闭笔记操作弹窗（如果存在）
    Navigator.of(context).pop();

    // 显示成功提示
    _showToast('移动笔记到：$destination', closeDialog: false);
  }

  String _getHeaderTitle() {
    if (_selectedKnowledgeBase == null) {
      return '选择知识库';
    }

    if (_folderPath.isEmpty) {
      return _selectedKnowledgeBase!.title;
    }

    return _folderPath.last['name'];
  }

  void _goBack() {
    setState(() {
      if (_folderPath.isNotEmpty) {
        // 返回上一级文件夹
        _folderPath.removeLast();
        _loadFolders();
      } else {
        // 返回知识库列表
        _selectedKnowledgeBase = null;
        _folders.clear();
        _folderPath.clear();
      }
    });
  }

  void _enterFolder(Map<String, dynamic> folder) {
    setState(() {
      _folderPath.add(folder);
      _isLoadingFolders = true;
    });

    _loadFolders();
  }

  void _loadFolders() {
    // 模拟加载文件夹数据
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          // 根据当前路径深度生成不同的文件夹数据
          if (_folderPath.isEmpty) {
            // 根目录文件夹
            _folders = [
              {'id': '1', 'name': '基础教程', 'itemCount': 12},
              {'id': '2', 'name': '进阶技巧', 'itemCount': 8},
              {'id': '3', 'name': '实战项目', 'itemCount': 15},
              {'id': '4', 'name': '常见问题', 'itemCount': 6},
            ];
          } else {
            // 子文件夹（模拟数据）
            final parentFolder = _folderPath.last;
            // 模拟某些文件夹为空的情况
            if (parentFolder['id'] == '4') {
              // "常见问题"文件夹为空，显示空状态
              _folders = [];
            } else {
              _folders = [
                {'id': '${parentFolder['id']}_1', 'name': '子文件夹1', 'itemCount': 5},
                {'id': '${parentFolder['id']}_2', 'name': '子文件夹2', 'itemCount': 3},
                {'id': '${parentFolder['id']}_3', 'name': '子文件夹3', 'itemCount': 8},
              ];
            }
          }
          _isLoadingFolders = false;
        });
      }
    });
  }

  void _showToast(String message, {bool closeDialog = false}) {
    if (closeDialog) {
      Navigator.of(context).pop();
    }

    // 延迟一下确保弹窗关闭后再显示toast
    Future.delayed(Duration(milliseconds: closeDialog ? 100 : 0), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('测试: $message'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    });
  }
}

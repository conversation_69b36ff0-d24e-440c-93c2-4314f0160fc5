import 'package:flutter/material.dart';
// 添加markdown解析包
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../theme/app_theme.dart';

class LinkProcessingBottomSheet extends StatefulWidget {
  final String link;
  final String customStyle;

  const LinkProcessingBottomSheet({
    super.key,
    required this.link,
    required this.customStyle,
  });

  static void show(
    BuildContext context, {
    required String link,
    required String customStyle,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      isDismissible: true,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.8,
            minChildSize: 0.3,
            maxChildSize: 0.9,
            builder:
                (context, scrollController) => LinkProcessingBottomSheet(
                  link: link,
                  customStyle: customStyle,
                ),
          ),
    );
  }

  @override
  State<LinkProcessingBottomSheet> createState() =>
      _LinkProcessingBottomSheetState();
}

class _LinkProcessingBottomSheetState extends State<LinkProcessingBottomSheet>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _shimmerController;

  int _currentStage = 0; // 0: 链接读取, 1: 链接分析, 2: AI写笔记
  final List<String> _stages = ['链接读取', '链接分析', 'AI写笔记'];

  String _streamContent = '';
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _startProcessing();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  void _startProcessing() {
    // 模拟处理过程
    _simulateStageProgress();
  }

  void _simulateStageProgress() async {
    // 阶段1: 链接读取
    setState(() => _currentStage = 0);
    await Future.delayed(const Duration(seconds: 2));

    // 阶段2: 链接分析
    setState(() => _currentStage = 1);
    await Future.delayed(const Duration(seconds: 3));

    // 阶段3: AI写笔记
    setState(() => _currentStage = 2);
    await Future.delayed(const Duration(seconds: 1));

    // 开始流式返回内容
    _simulateStreamContent();
  }

  void _simulateStreamContent() async {
    final content = '''# 链接笔记标题

## 主要内容概述
这是从链接中提取的主要内容，AI正在分析和整理中...

## 关键要点
- 要点一：重要信息的总结
- 要点二：核心观点的提炼
- 要点三：实用建议的整理

## 详细分析
基于链接内容的深度分析正在生成中，请稍候...

## 总结与思考
AI将根据您的自定义风格要求，生成符合需求的笔记内容。''';

    // 模拟流式返回
    for (int i = 0; i < content.length; i += 5) {
      if (mounted) {
        setState(() {
          _streamContent = content.substring(0, i + 5);
        });
        await Future.delayed(const Duration(milliseconds: 50));
      }
    }

    setState(() {
      _streamContent = content;
      _isCompleted = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _buildHandle(),
          _buildProgressHeader(),
          _buildTips(),
          Expanded(child: _buildContent()),
          if (_isCompleted) _buildCompleteActions(),
        ],
      ),
    );
  }

  Widget _buildHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppTheme.borderColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildProgressHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children:
                _stages.asMap().entries.map((entry) {
                  final index = entry.key;
                  final stage = entry.value;
                  final isActive = index <= _currentStage;
                  final isCurrent = index == _currentStage;

                  return Expanded(
                    child: Column(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color:
                                isActive
                                    ? AppTheme.primaryColor
                                    : AppTheme.borderColor,
                            shape: BoxShape.circle,
                          ),
                          child:
                              isCurrent && !_isCompleted
                                  ? SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  )
                                  : Icon(
                                    isActive ? Icons.check : Icons.circle,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          stage,
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            color:
                                isActive
                                    ? AppTheme.primaryColor
                                    : AppTheme.textSecondaryColor,
                            fontWeight:
                                isActive ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
          ),
          if (!_isCompleted) ...[
            const SizedBox(height: 16),
            LinearProgressIndicator(
              backgroundColor: AppTheme.borderColor.withValues(alpha: 0.3),
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTips() {
    if (_isCompleted) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.borderColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'AI已经开始工作了，大约需要1-10秒不等。',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textPrimaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '部分平台视频分享链接，需要更长时间处理。\n你可以下拉收起或开始新笔记，AI处理完成后会自动更新',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondaryColor,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.borderColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: _streamContent.isEmpty ? _buildSkeleton() : _buildStreamContent(),
    );
  }

  Widget _buildSkeleton() {
    return AnimatedBuilder(
      animation: _shimmerController,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildShimmerLine(width: 200),
            const SizedBox(height: 16),
            _buildShimmerLine(width: double.infinity),
            const SizedBox(height: 8),
            _buildShimmerLine(width: double.infinity),
            const SizedBox(height: 8),
            _buildShimmerLine(width: 150),
            const SizedBox(height: 16),
            _buildShimmerLine(width: 180),
            const SizedBox(height: 8),
            _buildShimmerLine(width: double.infinity),
            const SizedBox(height: 8),
            _buildShimmerLine(width: double.infinity),
            const SizedBox(height: 8),
            _buildShimmerLine(width: 120),
          ],
        );
      },
    );
  }

  Widget _buildShimmerLine({required double width}) {
    return Container(
      width: width,
      height: 16,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: LinearGradient(
          colors: [
            AppTheme.borderColor.withValues(alpha: 0.3),
            AppTheme.borderColor.withValues(alpha: 0.1),
            AppTheme.borderColor.withValues(alpha: 0.3),
          ],
          stops: const [0.0, 0.5, 1.0],
          begin: Alignment(-1.0 + _shimmerController.value * 2, 0.0),
          end: Alignment(1.0 + _shimmerController.value * 2, 0.0),
        ),
      ),
    );
  }

  Widget _buildStreamContent() {
    // 使用MarkdownBody替换原来的Text组件以支持markdown格式渲染
    return SingleChildScrollView(
      child: MarkdownBody(
        data: _streamContent,
        // 配置markdown样式
        styleSheet: MarkdownStyleSheet(
          p: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.textPrimaryColor,
            height: 1.6,
          ),
          h1: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: AppTheme.textPrimaryColor,
            fontWeight: FontWeight.bold,
          ),
          h2: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: AppTheme.textPrimaryColor,
            fontWeight: FontWeight.bold,
          ),
          h3: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppTheme.textPrimaryColor,
            fontWeight: FontWeight.bold,
          ),
          listBullet: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: AppTheme.textPrimaryColor),
          strong: const TextStyle(fontWeight: FontWeight.bold),
        ),
        // 处理链接点击事件
        onTapLink: (text, href, title) {
          if (href != null) {
            final uri = Uri.parse(href);
            launchUrl(uri);
          }
        },
      ),
    );
  }

  Widget _buildCompleteActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: AppTheme.borderColor, width: 1)),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: const BorderSide(color: AppTheme.borderColor),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                '关闭',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('笔记已保存'),
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                );
                // 添加导航到笔记详情页面的逻辑
                // 这里使用 MaterialPageRoute 进行页面跳转
                // 注意：实际使用时需要替换为真实的笔记详情页面组件
                /*
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const NoteDetailPage(), // 替换为实际的笔记详情页面
                  ),
                );
                */
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                '保存笔记',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

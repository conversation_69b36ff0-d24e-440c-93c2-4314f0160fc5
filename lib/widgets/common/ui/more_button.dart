import 'package:flutter/material.dart';
import '../../../theme/app_theme.dart';

/// 通用的更多操作按钮组件
///
/// 提供统一的样式和交互行为，可在各个页面复用
class MoreButton extends StatelessWidget {
  /// 点击回调函数
  final VoidCallback onTap;

  /// 按钮大小，默认为32
  final double size;

  /// 图标大小，默认为18
  final double iconSize;

  /// 自定义图标，默认为more_vert
  final IconData icon;

  /// 是否启用，默认为true
  final bool enabled;

  const MoreButton({
    super.key,
    required this.onTap,
    this.size = 32,
    this.iconSize = 18,
    this.icon = Icons.more_vert,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: enabled ? onTap : null,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(size / 2),
          border: Border.all(
            color: AppTheme.borderColor.withValues(alpha: 0.3),
            width: 0.5,
          ),
        ),
        child: Icon(
          icon,
          size: iconSize,
          color:
              enabled
                  ? AppTheme.textPrimaryColor.withValues(alpha: 0.7)
                  : AppTheme.textTertiaryColor.withValues(alpha: 0.5),
        ),
      ),
    );
  }
}

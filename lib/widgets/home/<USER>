import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

class FloatingActionButtons extends StatelessWidget {
  final VoidCallback onMoreTap;
  final VoidCallback onRecordTap;
  final VoidCallback onTextTap;

  const FloatingActionButtons({
    super.key,
    required this.onMoreTap,
    required this.onRecordTap,
    required this.onTextTap,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 32,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(32),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.12),
                offset: const Offset(0, 8),
                blurRadius: 32,
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                offset: const Offset(0, 2),
                blurRadius: 8,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildActionButton(
                icon: Icons.add_circle_outline,
                label: '更多',
                onTap: onMoreTap,
              ),
              _buildDivider(),
              _buildActionButton(
                icon: Icons.mic,
                label: '录音',
                onTap: onRecordTap,
                isPrimary: true,
              ),
              _buildDivider(),
              _buildActionButton(
                icon: Icons.edit_outlined,
                label: '文字',
                onTap: onTextTap,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isPrimary = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isPrimary
              ? AppTheme.primaryColor.withValues(alpha: 0.8) // 保持一定的视觉重点，但不那么强烈
              : Colors.transparent,
          borderRadius: BorderRadius.circular(24),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isPrimary
                  ? Colors.white // 恢复白色，在半透明背景上更清晰
                  : AppTheme.textPrimaryColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isPrimary
                    ? Colors.white // 恢复白色，在半透明背景上更清晰
                    : AppTheme.textPrimaryColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      width: 1,
      height: 24,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      color: AppTheme.borderColor,
    );
  }
}

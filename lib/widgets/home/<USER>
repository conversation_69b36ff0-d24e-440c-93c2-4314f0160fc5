import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

enum NoteType {
  all,      // 全部笔记
  text,     // 文字笔记
  audio,    // 录音笔记
  link,     // 链接笔记
  image,    // 图片笔记
}

class NoteFilterBottomSheet extends StatefulWidget {
  final NoteType selectedType;
  final Function(NoteType) onTypeSelected;

  const NoteFilterBottomSheet({
    super.key,
    required this.selectedType,
    required this.onTypeSelected,
  });

  static void show(
    BuildContext context, {
    required NoteType selectedType,
    required Function(NoteType) onTypeSelected,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => NoteFilterBottomSheet(
        selectedType: selectedType,
        onTypeSelected: onTypeSelected,
      ),
    );
  }

  @override
  State<NoteFilterBottomSheet> createState() => _NoteFilterBottomSheetState();
}

class _NoteFilterBottomSheetState extends State<NoteFilterBottomSheet> {
  late NoteType _selectedType;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.selectedType;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHandle(),
          _buildHeader(),
          _buildFilterOptions(),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppTheme.borderColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 8, 20, 4),
      child: Row(
        children: [
          Text(
            '快速筛选',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterOptions() {
    final options = [
      FilterOption(
        type: NoteType.all,
        title: '全部笔记',
        icon: Icons.note_outlined,
        description: '显示所有类型的笔记',
      ),
      FilterOption(
        type: NoteType.text,
        title: '文字笔记',
        icon: Icons.text_fields,
        description: '纯文字内容的笔记',
      ),
      FilterOption(
        type: NoteType.audio,
        title: '录音笔记',
        icon: Icons.mic,
        description: '包含音频录制的笔记',
      ),
      FilterOption(
        type: NoteType.link,
        title: '链接笔记',
        icon: Icons.link,
        description: '包含网页链接的笔记',
      ),
      FilterOption(
        type: NoteType.image,
        title: '图片笔记',
        icon: Icons.image,
        description: '包含图片内容的笔记',
      ),
    ];

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: options.length,
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        color: AppTheme.borderColor,
      ),
      itemBuilder: (context, index) {
        final option = options[index];
        final isSelected = _selectedType == option.type;
        
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(vertical: 4),
          leading: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: isSelected 
                  ? AppTheme.primaryColor.withValues(alpha: 0.1)
                  : AppTheme.borderColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              option.icon,
              color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondaryColor,
              size: 24,
            ),
          ),
          title: Text(
            option.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: isSelected ? AppTheme.primaryColor : AppTheme.textPrimaryColor,
            ),
          ),
          subtitle: Text(
            option.description,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          trailing: isSelected
              ? const Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryColor,
                  size: 24,
                )
              : null,
          onTap: () {
            setState(() {
              _selectedType = option.type;
            });
            widget.onTypeSelected(option.type);
            Navigator.of(context).pop();
          },
        );
      },
    );
  }
}

class FilterOption {
  final NoteType type;
  final String title;
  final IconData icon;
  final String description;

  const FilterOption({
    required this.type,
    required this.title,
    required this.icon,
    required this.description,
  });
}

extension NoteTypeExtension on NoteType {
  String get displayName {
    switch (this) {
      case NoteType.all:
        return '全部笔记';
      case NoteType.text:
        return '文字笔记';
      case NoteType.audio:
        return '录音笔记';
      case NoteType.link:
        return '链接笔记';
      case NoteType.image:
        return '图片笔记';
    }
  }
}

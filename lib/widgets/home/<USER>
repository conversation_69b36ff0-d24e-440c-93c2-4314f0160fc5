import 'package:flutter/material.dart';
import '../../models/note.dart';
import '../../theme/app_theme.dart';
import '../../utils/time_utils.dart';
import '../common/more_button.dart';
import '../common/note_operations_bottom_sheet.dart';

class NoteCard extends StatelessWidget {
  final Note note;
  final VoidCallback onTap;

  const NoteCard({
    super.key,
    required this.note,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.cardColor,
          borderRadius: BorderRadius.circular(20),  // 使用现代化圆角
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryColor.withValues(alpha: 0.08),
              offset: const Offset(0, 4),
              blurRadius: 16,
              spreadRadius: 0,
            ),
          ],
          border: Border.all(
            color: AppTheme.borderColor,
            width: 0.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 第一行：标题
            Text(
              note.title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                height: 1.3,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),

            // 中间：内容
            Text(
              note.content,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
                height: 1.5,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),

            // 如果有图片，显示在内容下方
            if (note.images.isNotEmpty) ...[
              const SizedBox(height: 12),
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: AspectRatio(
                  aspectRatio: 16 / 9,  // 固定宽高比，更统一
                  child: Image.network(
                    note.images.first,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: AppTheme.backgroundColor,
                        child: const Center(
                          child: Icon(
                            Icons.image_not_supported,
                            color: AppTheme.textSecondaryColor,
                            size: 32,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],

            const SizedBox(height: 16),

            // 最后一排：发布时间和操作按钮
            Row(
              children: [
                // 发布时间
                Expanded(
                  child: Text(
                    TimeUtils.formatPublishTime(note.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textTertiaryColor,
                    ),
                  ),
                ),
                // 操作按钮
                MoreButton(
                  onTap: () => _showActionMenu(context),
                  icon: Icons.more_horiz,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 显示操作菜单
  void _showActionMenu(BuildContext context) {
    // 模拟知识库关联状态（实际应该从数据库获取）
    String? knowledgeBaseName;
    if (note.id.hashCode % 3 == 0) {
      knowledgeBaseName = 'Flutter开发指南';
    }

    NoteOperationsBottomSheet.show(
      context,
      note,
      knowledgeBaseName: knowledgeBaseName,
    );
  }

}

import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../theme/app_theme.dart';
import '../../pages/knowledge_base/knowledge_base_list_page.dart';
import 'knowledge_base_card.dart';

class KnowledgeBaseSection extends StatelessWidget {
  final List<KnowledgeBase> knowledgeBases;
  final Function(KnowledgeBase) onKnowledgeBaseTap;

  const KnowledgeBaseSection({
    super.key,
    required this.knowledgeBases,
    required this.onKnowledgeBaseTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题栏
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '知识库',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.w600,  // 与笔记标题保持一致
                  height: 1.2,  // 减少行高，让标题更紧凑
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const KnowledgeBaseListPage(),
                    ),
                  );
                },
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // 与笔记部分保持一致
                  minimumSize: Size.zero,  // 移除最小尺寸限制
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap, // 紧凑点击区域
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '查看更多',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Icon(
                      Icons.arrow_forward_ios,
                      size: 14,
                      color: AppTheme.primaryColor,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),  // 与笔记部分保持一致的间距
        // 横向滚动的知识库卡片列表
        SizedBox(
          height: 130,  // 增加高度，让卡片不那么拥挤
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: knowledgeBases.length,
            itemBuilder: (context, index) {
              final knowledgeBase = knowledgeBases[index];
              return KnowledgeBaseCard(
                knowledgeBase: knowledgeBase,
                onTap: () => onKnowledgeBaseTap(knowledgeBase),
              );
            },
          ),
        ),
      ],
    );
  }
}

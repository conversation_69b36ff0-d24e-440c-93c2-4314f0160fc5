import 'package:flutter/material.dart';
import '../../models/user.dart';
import '../../theme/app_theme.dart';
import '../../pages/search/search_page.dart';

class SearchHeader extends StatefulWidget {
  final User currentUser;
  final VoidCallback onProfileTap;
  final Function(String) onSearchChanged;

  const SearchHeader({
    super.key,
    required this.currentUser,
    required this.onProfileTap,
    required this.onSearchChanged,
  });

  @override
  State<SearchHeader> createState() => _SearchHeaderState();
}

class _SearchHeaderState extends State<SearchHeader> {
  final TextEditingController _searchController = TextEditingController();
  bool _showClearButton = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchTextChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchTextChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchTextChanged() {
    final hasText = _searchController.text.isNotEmpty;
    if (_showClearButton != hasText) {
      setState(() {
        _showClearButton = hasText;
      });
    }
    widget.onSearchChanged(_searchController.text);
  }

  void _clearSearch() {
    _searchController.clear();
    widget.onSearchChanged('');
  }

  void _onSearchTap() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SearchPage(
          initialQuery: _searchController.text,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      decoration: _buildHeaderDecoration(),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            Expanded(child: _buildSearchBox(context)),
            const SizedBox(width: 12),
            _buildUserAvatar(),
          ],
        ),
      ),
    );
  }

  BoxDecoration _buildHeaderDecoration() {
    return const BoxDecoration(
      color: AppTheme.surfaceColor,
      boxShadow: [
        BoxShadow(
          color: AppTheme.shadowColor,
          offset: Offset(0, 2),
          blurRadius: 8,
        ),
      ],
    );
  }

  Widget _buildSearchBox(BuildContext context) {
    return GestureDetector(
      onTap: _onSearchTap,
      child: Container(
        height: 44,
        decoration: _buildSearchBoxDecoration(),
        child: Row(
          children: [
            _buildSearchIcon(),
            Expanded(child: _buildSearchText(context)),
            if (_showClearButton) _buildClearButton(),
          ],
        ),
      ),
    );
  }

  BoxDecoration _buildSearchBoxDecoration() {
    return BoxDecoration(
      color: AppTheme.searchBoxColor,
      borderRadius: BorderRadius.circular(22),
      boxShadow: const [
        BoxShadow(
          color: AppTheme.searchBoxShadow,
          offset: Offset(0, 1),
          blurRadius: 4,
          spreadRadius: 0,
        ),
      ],
    );
  }

  Widget _buildSearchIcon() {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Icon(
        Icons.search,
        color: AppTheme.textSecondaryColor,
        size: 20,
      ),
    );
  }

  Widget _buildSearchText(BuildContext context) {
    final isEmpty = _searchController.text.isEmpty;
    return Text(
      isEmpty ? '搜索知识库、笔记...' : _searchController.text,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: isEmpty ? AppTheme.textSecondaryColor : AppTheme.textPrimaryColor,
      ),
    );
  }

  Widget _buildClearButton() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: _clearSearch,
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppTheme.textSecondaryColor.withValues(alpha: 0.08),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.close_rounded,
              color: AppTheme.textSecondaryColor,
              size: 12,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserAvatar() {
    return GestureDetector(
      onTap: widget.onProfileTap,
      child: Container(
        width: 44,
        height: 44,
        decoration: _buildAvatarDecoration(),
        child: ClipOval(
          child: widget.currentUser.avatar.isNotEmpty
              ? Image.network(
                  widget.currentUser.avatar,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(),
                )
              : _buildDefaultAvatar(),
        ),
      ),
    );
  }

  BoxDecoration _buildAvatarDecoration() {
    return BoxDecoration(
      shape: BoxShape.circle,
      border: Border.all(
        color: AppTheme.primaryColor.withValues(alpha: 0.6), // 适中的透明度，既不太深也不太淡
        width: 2,
      ),
      boxShadow: [
        BoxShadow(
          color: AppTheme.primaryColor.withValues(alpha: 0.15), // 稍微明显一点的阴影
          offset: const Offset(0, 2),
          blurRadius: 6,
          spreadRadius: 0,
        ),
      ],
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.8),
            AppTheme.secondaryColor.withValues(alpha: 0.8),
          ], // 保留渐变但降低饱和度
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: const Icon(
        Icons.person,
        color: Colors.white,
        size: 24,
      ),
    );
  }
}

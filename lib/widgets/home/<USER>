import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../theme/app_theme.dart';

class KnowledgeBaseCard extends StatelessWidget {
  final KnowledgeBase knowledgeBase;
  final VoidCallback onTap;

  const KnowledgeBaseCard({
    super.key,
    required this.knowledgeBase,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 200,
        margin: const EdgeInsets.only(right: 16),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16), // 增加内边距，让内容更舒适
        decoration: _buildCardDecoration(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween, // 让内容在垂直方向均匀分布
          children: [
            // 上半部分：标题和统计信息
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTitle(context),
                const SizedBox(height: 12), // 进一步增加间距，让标题和统计信息更舒适
                _buildStatistics(context),
              ],
            ),
            // 下半部分：作者信息
            _buildAuthorInfo(context),
          ],
        ),
      ),
    );
  }

  // 构建卡片装饰
  BoxDecoration _buildCardDecoration() {
    return BoxDecoration(
      color: AppTheme.cardColor,
      borderRadius: BorderRadius.circular(16),
      boxShadow: const [
        BoxShadow(
          color: AppTheme.shadowColor,
          offset: Offset(0, 4),
          blurRadius: 12,
        ),
      ],
    );
  }

  // 构建标题
  Widget _buildTitle(BuildContext context) {
    return Text(
      knowledgeBase.title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
        height: 1.2,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  // 构建统计信息
  Widget _buildStatistics(BuildContext context) {
    return Row(
      children: [
        Flexible(
          child: _buildStatItem(
            icon: Icons.article_outlined,
            text: '${knowledgeBase.contentCount}个内容',
            context: context,
          ),
        ),
        const SizedBox(width: 12), // 增加间距，让统计信息更清晰
        Flexible(
          child: _buildStatItem(
            icon: Icons.people_outline,
            text: '${knowledgeBase.userCount}人使用',
            context: context,
          ),
        ),
      ],
    );
  }

  // 构建作者信息
  Widget _buildAuthorInfo(BuildContext context) {
    return Row(
      children: [
        _buildAuthorAvatar(),
        const SizedBox(width: 8),  // 增加头像和文字间距，更舒适
        Expanded(
          child: Text(
            knowledgeBase.author,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  // 构建作者头像
  Widget _buildAuthorAvatar() {
    return CircleAvatar(
      radius: 10,  // 稍微减小头像尺寸
      backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.8), // 使用半透明背景，更柔和
      backgroundImage: knowledgeBase.authorAvatar.isNotEmpty
          ? NetworkImage(knowledgeBase.authorAvatar)
          : null,
      child: knowledgeBase.authorAvatar.isEmpty
          ? const Icon(
              Icons.person,
              size: 12,  // 相应减小图标尺寸
              color: Colors.white,
            )
          : null,
    );
  }

  // 构建统计项
  Widget _buildStatItem({
    required IconData icon,
    required String text,
    required BuildContext context,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: AppTheme.textSecondaryColor,
        ),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }
}

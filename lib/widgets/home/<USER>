import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../pages/auth/login_page.dart';
import '../../services/auth_service.dart';

class WelcomeBanner extends StatelessWidget {
  final VoidCallback onTap;

  const WelcomeBanner({
    super.key,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _handleTap(context),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.all(20),
        decoration: _buildBannerDecoration(),
        child: Row(
          children: [
            _buildIcon(),
            const SizedBox(width: 16),
            Expanded(child: _buildContent(context)),
            _buildArrowIcon(),
          ],
        ),
      ),
    );
  }

  // 简洁现代的装饰
  BoxDecoration _buildBannerDecoration() {
    return BoxDecoration(
      color: AppTheme.cardColor,  // 使用卡片颜色，更协调
      borderRadius: BorderRadius.circular(20),
      border: Border.all(
        color: AppTheme.borderColor,
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: AppTheme.primaryColor.withValues(alpha: 0.06),
          offset: const Offset(0, 2),
          blurRadius: 8,
          spreadRadius: 0,
        ),
      ],
    );
  }

  // 简洁的图标
  Widget _buildIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Icon(
        Icons.chat_bubble_outline,
        size: 24,
        color: AppTheme.primaryColor,
      ),
    );
  }

  // 简洁的内容区域
  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '欢迎来到VideoSeek',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '现在，开始你的灵感之旅吧',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  // 简洁的箭头图标
  Widget _buildArrowIcon() {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppTheme.primaryColor,
      ),
    );
  }

  // 处理点击事件，检查登录状态
  void _handleTap(BuildContext context) async {
    final authService = AuthService();

    if (!authService.isLoggedIn) {
      // 未登录，跳转到登录页面
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const LoginPage(),
        ),
      );

      // 如果登录成功，再执行原来的回调
      if (result == true) {
        onTap();
      }
    } else {
      // 已登录，直接执行回调
      onTap();
    }
  }
}

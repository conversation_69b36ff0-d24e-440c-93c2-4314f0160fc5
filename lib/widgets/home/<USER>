import 'package:flutter/material.dart';
import '../../models/note.dart';
import '../../theme/app_theme.dart';
import 'note_card.dart';
import 'note_filter_bottom_sheet.dart';
import 'note_sort_bottom_sheet.dart';

class NotesSection extends StatefulWidget {
  final List<Note> notes;
  final Function(Note) onNoteTap;

  const NotesSection({
    super.key,
    required this.notes,
    required this.onNoteTap,
  });

  @override
  State<NotesSection> createState() => _NotesSectionState();
}

class _NotesSectionState extends State<NotesSection> {
  NoteType _selectedNoteType = NoteType.all;
  NoteSortType _selectedSortType = NoteSortType.modifiedTime;
  SortOrder _selectedSortOrder = SortOrder.descending;

  List<Note> get _filteredAndSortedNotes {
    List<Note> filtered = widget.notes;

    // 筛选逻辑（这里简化处理，实际应该根据笔记类型筛选）
    // 在实际应用中，Note模型应该包含类型字段

    // 排序逻辑
    filtered.sort((a, b) {
      DateTime timeA, timeB;

      if (_selectedSortType == NoteSortType.modifiedTime) {
        // 假设使用 updatedAt 字段，如果没有则使用 createdAt
        timeA = a.createdAt; // 实际应该是 a.updatedAt ?? a.createdAt
        timeB = b.createdAt; // 实际应该是 b.updatedAt ?? b.createdAt
      } else {
        timeA = a.createdAt;
        timeB = b.createdAt;
      }

      return _selectedSortOrder == SortOrder.descending
          ? timeB.compareTo(timeA)
          : timeA.compareTo(timeB);
    });

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题栏
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              // 筛选按钮
              Expanded(
                child: GestureDetector(
                  onTap: _showFilterBottomSheet,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _selectedNoteType.displayName,
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          height: 1.2,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(
                        Icons.keyboard_arrow_down,
                        color: AppTheme.textSecondaryColor,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
              // 排序按钮
              IconButton(
                onPressed: _showSortBottomSheet,
                icon: const Icon(
                  Icons.view_list,
                  color: AppTheme.textSecondaryColor,
                  size: 24,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),   // 适中的标题下方间距
        // 单列笔记列表
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.zero,  // 移除ListView默认的padding
            itemCount: _filteredAndSortedNotes.length,
            itemBuilder: (context, index) {
              final note = _filteredAndSortedNotes[index];
              return NoteCard(
                note: note,
                onTap: () => widget.onNoteTap(note),
              );
            },
          ),
        ),
      ],
    );
  }

  void _showFilterBottomSheet() {
    NoteFilterBottomSheet.show(
      context,
      selectedType: _selectedNoteType,
      onTypeSelected: (type) {
        setState(() {
          _selectedNoteType = type;
        });
      },
    );
  }

  void _showSortBottomSheet() {
    NoteSortBottomSheet.show(
      context,
      selectedSortType: _selectedSortType,
      selectedSortOrder: _selectedSortOrder,
      onSortChanged: (sortType, sortOrder) {
        setState(() {
          _selectedSortType = sortType;
          _selectedSortOrder = sortOrder;
        });
      },
    );
  }
}

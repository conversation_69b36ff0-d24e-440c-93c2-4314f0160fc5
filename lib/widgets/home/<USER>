import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

enum NoteSortType {
  modifiedTime,  // 最后修改时间
  createdTime,   // 创建时间
}

enum SortOrder {
  descending,    // 由近到远
  ascending,     // 由远到近
}

class NoteSortBottomSheet extends StatefulWidget {
  final NoteSortType selectedSortType;
  final SortOrder selectedSortOrder;
  final Function(NoteSortType, SortOrder) onSortChanged;

  const NoteSortBottomSheet({
    super.key,
    required this.selectedSortType,
    required this.selectedSortOrder,
    required this.onSortChanged,
  });

  static void show(
    BuildContext context, {
    required NoteSortType selectedSortType,
    required SortOrder selectedSortOrder,
    required Function(NoteSortType, SortOrder) onSortChanged,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => NoteSortBottomSheet(
        selectedSortType: selectedSortType,
        selectedSortOrder: selectedSortOrder,
        onSortChanged: onSortChanged,
      ),
    );
  }

  @override
  State<NoteSortBottomSheet> createState() => _NoteSortBottomSheetState();
}

class _NoteSortBottomSheetState extends State<NoteSortBottomSheet> {
  late NoteSortType _selectedSortType;
  late SortOrder _selectedSortOrder;

  @override
  void initState() {
    super.initState();
    _selectedSortType = widget.selectedSortType;
    _selectedSortOrder = widget.selectedSortOrder;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHandle(),
          _buildHeader(),
          _buildSortOptions(),
          const SizedBox(height: 16),
          _buildSortOrderOptions(),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppTheme.borderColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 8, 20, 4),
      child: Row(
        children: [
          Text(
            '排序',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            '排序方式',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ),
        const SizedBox(height: 12),
        _buildSortTypeOption(
          type: NoteSortType.modifiedTime,
          title: '最后修改时间',
          icon: Icons.edit_outlined,
        ),
        _buildSortTypeOption(
          type: NoteSortType.createdTime,
          title: '创建时间',
          icon: Icons.add_circle_outline,
        ),
      ],
    );
  }

  Widget _buildSortTypeOption({
    required NoteSortType type,
    required String title,
    required IconData icon,
  }) {
    final isSelected = _selectedSortType == type;
    
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      leading: Icon(
        icon,
        color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondaryColor,
        size: 24,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w500,
          color: isSelected ? AppTheme.primaryColor : AppTheme.textPrimaryColor,
        ),
      ),
      trailing: isSelected
          ? const Icon(
              Icons.check_circle,
              color: AppTheme.primaryColor,
              size: 20,
            )
          : null,
      onTap: () {
        setState(() {
          _selectedSortType = type;
        });
        _updateSort();
      },
    );
  }

  Widget _buildSortOrderOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            '排序顺序',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ),
        const SizedBox(height: 12),
        _buildSortOrderOption(
          order: SortOrder.descending,
          title: '由近到远',
          icon: Icons.arrow_downward,
        ),
        _buildSortOrderOption(
          order: SortOrder.ascending,
          title: '由远到近',
          icon: Icons.arrow_upward,
        ),
      ],
    );
  }

  Widget _buildSortOrderOption({
    required SortOrder order,
    required String title,
    required IconData icon,
  }) {
    final isSelected = _selectedSortOrder == order;
    
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      leading: Icon(
        icon,
        color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondaryColor,
        size: 24,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w500,
          color: isSelected ? AppTheme.primaryColor : AppTheme.textPrimaryColor,
        ),
      ),
      trailing: isSelected
          ? const Icon(
              Icons.check_circle,
              color: AppTheme.primaryColor,
              size: 20,
            )
          : null,
      onTap: () {
        setState(() {
          _selectedSortOrder = order;
        });
        _updateSort();
      },
    );
  }

  void _updateSort() {
    widget.onSortChanged(_selectedSortType, _selectedSortOrder);
    Navigator.of(context).pop();
  }
}

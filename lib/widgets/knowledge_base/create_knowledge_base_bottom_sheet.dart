import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

class CreateKnowledgeBaseBottomSheet extends StatelessWidget {
  const CreateKnowledgeBaseBottomSheet({super.key});

  static void show(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => const CreateKnowledgeBaseBottomSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHandle(),
          _buildHeader(context),
          _buildOptionsList(context),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppTheme.borderColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 8, 20, 4),
      child: Row(
        children: [
          Text(
            '新建知识库',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionsList(BuildContext context) {
    final options = [
      CreateOption(
        icon: Icons.create_new_folder,
        title: '创建空白知识库',
        subtitle: '从零开始构建知识库',
        onTap: () => _showToast(context, '创建空白知识库'),
      ),
      CreateOption(
        icon: Icons.upload_file,
        title: '导入文档',
        subtitle: '从文档文件创建知识库',
        onTap: () => _showToast(context, '导入文档'),
      ),
      CreateOption(
        icon: Icons.link,
        title: '从网页导入',
        subtitle: '从网页链接创建知识库',
        onTap: () => _showToast(context, '从网页导入'),
      ),
      CreateOption(
        icon: Icons.content_copy,
        title: '复制现有知识库',
        subtitle: '基于现有知识库创建副本',
        onTap: () => _showToast(context, '复制现有知识库'),
      ),
    ];

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: options.length,
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        color: AppTheme.borderColor,
      ),
      itemBuilder: (context, index) {
        final option = options[index];
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(vertical: 4),
          leading: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              option.icon,
              color: AppTheme.primaryColor,
              size: 24,
            ),
          ),
          title: Text(
            option.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          subtitle: Text(
            option.subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          trailing: const Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: AppTheme.textSecondaryColor,
          ),
          onTap: () {
            Navigator.of(context).pop();
            option.onTap();
          },
        );
      },
    );
  }

  void _showToast(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('测试: $message'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

class CreateOption {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const CreateOption({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });
}

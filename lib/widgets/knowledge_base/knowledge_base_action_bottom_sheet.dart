import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../theme/app_theme.dart';

class KnowledgeBaseActionBottomSheet extends StatelessWidget {
  final KnowledgeBase knowledgeBase;

  const KnowledgeBaseActionBottomSheet({
    super.key,
    required this.knowledgeBase,
  });

  static void show(BuildContext context, KnowledgeBase knowledgeBase) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => KnowledgeBaseActionBottomSheet(
        knowledgeBase: knowledgeBase,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHandle(),
          _buildHeader(context),
          _buildActionsList(context),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppTheme.borderColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 8, 20, 4),
      child: Row(
        children: [
          Text(
            '知识库操作',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsList(BuildContext context) {
    final actions = [
      ActionItem(
        icon: Icons.edit,
        title: '编辑知识库',
        subtitle: '修改知识库信息',
        onTap: () => _showToast(context, '编辑知识库'),
      ),
      ActionItem(
        icon: Icons.security,
        title: '权限设置',
        subtitle: '管理访问权限',
        onTap: () => _showToast(context, '权限设置'),
      ),
      ActionItem(
        icon: Icons.storage,
        title: '知识库容量',
        subtitle: '查看存储使用情况',
        onTap: () => _showToast(context, '知识库容量'),
      ),
      ActionItem(
        icon: Icons.delete,
        title: '删除知识库',
        subtitle: '永久删除此知识库',
        onTap: () => _showDeleteConfirmDialog(context),
        isDestructive: true,
      ),
    ];

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: actions.length,
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        color: AppTheme.borderColor,
      ),
      itemBuilder: (context, index) {
        final action = actions[index];
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(vertical: 4),
          leading: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: action.isDestructive
                  ? Colors.red.withValues(alpha: 0.1)
                  : AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              action.icon,
              color: action.isDestructive ? Colors.red : AppTheme.primaryColor,
              size: 24,
            ),
          ),
          title: Text(
            action.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: action.isDestructive ? Colors.red : AppTheme.textPrimaryColor,
            ),
          ),
          subtitle: Text(
            action.subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          trailing: Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: action.isDestructive ? Colors.red : AppTheme.textSecondaryColor,
          ),
          onTap: () {
            Navigator.of(context).pop();
            action.onTap();
          },
        );
      },
    );
  }

  void _showToast(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('测试: $message'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _showDeleteConfirmDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除知识库'),
        content: Text('确定要删除"${knowledgeBase.title}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showToast(context, '删除知识库');
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}

class ActionItem {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  final bool isDestructive;

  const ActionItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
    this.isDestructive = false,
  });
}

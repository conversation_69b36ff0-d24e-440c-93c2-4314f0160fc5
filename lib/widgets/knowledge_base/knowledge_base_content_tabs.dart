import 'package:flutter/material.dart';
import '../../models/note.dart';
import '../../theme/app_theme.dart';
import '../home/<USER>';

enum ContentType { notes, files, authors }

class KnowledgeBaseContentTabs extends StatelessWidget {
  final ContentType contentType;
  final List<Map<String, dynamic>> items;
  final Function(Map<String, dynamic>) onItemTap;
  final Function(Map<String, dynamic>)? onItemAction;

  const KnowledgeBaseContentTabs({
    super.key,
    required this.contentType,
    required this.items,
    required this.onItemTap,
    this.onItemAction,
  });

  @override
  Widget build(BuildContext context) {
    if (items.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: items.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildContentItem(context, item);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    String title;
    IconData icon;

    switch (contentType) {
      case ContentType.notes:
        title = '暂无笔记';
        icon = Icons.note_outlined;
        break;
      case ContentType.files:
        title = '暂无文件';
        icon = Icons.folder_outlined;
        break;
      case ContentType.authors:
        title = '暂无订阅博主';
        icon = Icons.person_outline;
        break;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppTheme.textSecondaryColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppTheme.textSecondaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildContentItem(BuildContext context, Map<String, dynamic> item) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onItemTap(item),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: _buildItemContent(context, item),
          ),
        ),
      ),
    );
  }

  Widget _buildItemContent(BuildContext context, Map<String, dynamic> item) {
    switch (contentType) {
      case ContentType.notes:
        return _buildNoteCard(context, item);
      case ContentType.files:
        return _buildFileContent(context, item);
      case ContentType.authors:
        return _buildAuthorContent(context, item);
    }
  }

  // 将Map数据转换为Note对象
  Note _mapToNote(Map<String, dynamic> noteMap) {
    return Note(
      id: noteMap['id'] ?? '',
      title: noteMap['title'] ?? '',
      content: noteMap['content'] ?? '',
      author: noteMap['author'] ?? '知识库作者',
      authorAvatar:
          noteMap['authorAvatar'] ?? 'https://picsum.photos/200/300?random=1',
      images: List<String>.from(noteMap['images'] ?? []),
      tags: List<String>.from(noteMap['tags'] ?? []),
      likeCount: noteMap['likeCount'] ?? 0,
      commentCount: noteMap['commentCount'] ?? 0,
      createdAt: noteMap['publishTime'] ?? DateTime.now(),
      updatedAt:
          noteMap['updatedAt'] ?? noteMap['publishTime'] ?? DateTime.now(),
    );
  }

  Widget _buildNoteCard(BuildContext context, Map<String, dynamic> noteMap) {
    final note = _mapToNote(noteMap);
    return NoteCard(note: note, onTap: () => onItemTap(noteMap));
  }

  Widget _buildFileContent(BuildContext context, Map<String, dynamic> file) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.folder,
            color: AppTheme.primaryColor,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                file['title'] ?? '',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                '${file['contentCount'] ?? 0}个内容',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
        const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppTheme.textSecondaryColor,
        ),
      ],
    );
  }

  Widget _buildAuthorContent(
    BuildContext context,
    Map<String, dynamic> author,
  ) {
    return Row(
      children: [
        CircleAvatar(
          radius: 24,
          backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
          backgroundImage:
              author['avatar'] != null ? NetworkImage(author['avatar']) : null,
          child:
              author['avatar'] == null
                  ? const Icon(
                    Icons.person,
                    color: AppTheme.primaryColor,
                    size: 24,
                  )
                  : null,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                author['name'] ?? '',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                '${author['noteCount'] ?? 0}条笔记',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
        const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppTheme.textSecondaryColor,
        ),
      ],
    );
  }
}

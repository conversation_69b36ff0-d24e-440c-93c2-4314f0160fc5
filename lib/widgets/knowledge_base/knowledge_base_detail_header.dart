import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../theme/app_theme.dart';
import '../../utils/time_utils.dart';

class KnowledgeBaseDetailHeader extends StatelessWidget {
  final KnowledgeBase knowledgeBase;

  const KnowledgeBaseDetailHeader({
    super.key,
    required this.knowledgeBase,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: AppTheme.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 知识库名称
          Text(
            knowledgeBase.title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 16),
          
          // 作者信息
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
                backgroundImage: knowledgeBase.authorAvatar.isNotEmpty 
                    ? NetworkImage(knowledgeBase.authorAvatar) 
                    : null,
                child: knowledgeBase.authorAvatar.isEmpty 
                    ? const Icon(
                        Icons.person,
                        color: AppTheme.primaryColor,
                        size: 20,
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      knowledgeBase.author,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      TimeUtils.formatCreateTime(knowledgeBase.createdAt),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 统计信息
          Row(
            children: [
              _buildStatItem(
                context,
                '${knowledgeBase.contentCount}个内容',
                Icons.article_outlined,
              ),
              const SizedBox(width: 24),
              _buildStatItem(
                context,
                '${knowledgeBase.userCount}人在用',
                Icons.people_outline,
              ),
              const Spacer(),
              _buildPermissionBadge(context),
            ],
          ),
          
          // 描述信息
          if (knowledgeBase.description.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              knowledgeBase.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
                height: 1.5,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String text, IconData icon) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: AppTheme.textSecondaryColor,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildPermissionBadge(BuildContext context) {
    String text;
    Color color;
    
    switch (knowledgeBase.permission) {
      case KnowledgeBasePermission.public:
        text = '公开';
        color = Colors.green;
        break;
      case KnowledgeBasePermission.team:
        text = '团队';
        color = Colors.orange;
        break;
      case KnowledgeBasePermission.private:
        text = '私密';
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }


}

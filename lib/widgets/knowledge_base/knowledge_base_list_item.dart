import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../theme/app_theme.dart';
import '../common/more_button.dart';

class KnowledgeBaseListItem extends StatelessWidget {
  final KnowledgeBase knowledgeBase;
  final VoidCallback onTap;
  final VoidCallback onMoreTap;

  const KnowledgeBaseListItem({
    super.key,
    required this.knowledgeBase,
    required this.onTap,
    required this.onMoreTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // 封面
                _buildCover(),
                const SizedBox(width: 12),
                // 内容区域
                Expanded(
                  child: _buildContent(context),
                ),
                // 操作按钮
                MoreButton(onTap: onMoreTap),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCover() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
      ),
      child: knowledgeBase.coverUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                knowledgeBase.coverUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultCover(),
              ),
            )
          : _buildDefaultCover(),
    );
  }

  Widget _buildDefaultCover() {
    return Icon(
      Icons.folder,
      color: AppTheme.primaryColor,
      size: 32,
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Text(
          knowledgeBase.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        // 权限标识
        _buildPermissionBadge(context),
        const SizedBox(height: 4),
        // 统计信息
        _buildStats(context),
      ],
    );
  }

  Widget _buildPermissionBadge(BuildContext context) {
    String text;
    Color color;
    
    switch (knowledgeBase.permission) {
      case KnowledgeBasePermission.public:
        text = '公开';
        color = Colors.green;
        break;
      case KnowledgeBasePermission.team:
        text = '团队';
        color = Colors.orange;
        break;
      case KnowledgeBasePermission.private:
        text = '私密';
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildStats(BuildContext context) {
    return Text(
      '${knowledgeBase.contentCount}个内容 · ${knowledgeBase.userCount}人在用',
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: AppTheme.textSecondaryColor,
      ),
    );
  }


}

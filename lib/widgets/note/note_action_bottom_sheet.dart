import 'package:flutter/material.dart';
import '../../models/note.dart';
import '../../theme/app_theme.dart';

class NoteActionBottomSheet extends StatelessWidget {
  final Note note;

  const NoteActionBottomSheet({
    super.key,
    required this.note,
  });

  static void show(BuildContext context, Note note) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => NoteActionBottomSheet(note: note),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHandle(),
          _buildHeader(context),
          _buildActionsList(context),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppTheme.borderColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 8, 20, 4),
      child: Row(
        children: [
          Text(
            '笔记操作',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsList(BuildContext context) {
    final actions = [
      ActionItem(
        icon: Icons.copy,
        title: '复制笔记',
        subtitle: '复制笔记内容到剪贴板',
        onTap: () => _copyNote(context),
      ),
      ActionItem(
        icon: Icons.folder_open,
        title: '添加到知识库',
        subtitle: '将笔记添加到指定知识库',
        onTap: () => _addToKnowledgeBase(context),
      ),
      ActionItem(
        icon: Icons.delete,
        title: '删除',
        subtitle: '永久删除此笔记',
        onTap: () => _deleteNote(context),
        isDestructive: true,
      ),
    ];

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: actions.length,
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        color: AppTheme.borderColor,
      ),
      itemBuilder: (context, index) {
        final action = actions[index];
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(vertical: 4),
          leading: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: action.isDestructive
                  ? Colors.red.withValues(alpha: 0.1)
                  : AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              action.icon,
              color: action.isDestructive ? Colors.red : AppTheme.primaryColor,
              size: 24,
            ),
          ),
          title: Text(
            action.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: action.isDestructive ? Colors.red : AppTheme.textPrimaryColor,
            ),
          ),
          subtitle: Text(
            action.subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          trailing: Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: action.isDestructive ? Colors.red : AppTheme.textSecondaryColor,
          ),
          onTap: () {
            Navigator.of(context).pop();
            action.onTap();
          },
        );
      },
    );
  }

  void _copyNote(BuildContext context) {
    // TODO: 实现复制笔记功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已复制笔记: ${note.title}'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _addToKnowledgeBase(BuildContext context) {
    // TODO: 实现添加到知识库功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('添加笔记到知识库: ${note.title}'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _deleteNote(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除笔记'),
        content: Text('确定要删除"${note.title}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('已删除笔记: ${note.title}'),
                  duration: const Duration(seconds: 2),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              );
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}

class ActionItem {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  final bool isDestructive;

  const ActionItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
    this.isDestructive = false,
  });
}

import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

class SearchPageHeader extends StatefulWidget {
  final String initialQuery;
  final Function(String) onSearchChanged;
  final VoidCallback onCancel;

  const SearchPageHeader({
    super.key,
    required this.initialQuery,
    required this.onSearchChanged,
    required this.onCancel,
  });

  @override
  State<SearchPageHeader> createState() => _SearchPageHeaderState();
}

class _SearchPageHeaderState extends State<SearchPageHeader> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _searchController.text = widget.initialQuery;
    
    // 自动聚焦搜索框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _clearSearch() {
    _searchController.clear();
    widget.onSearchChanged('');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      decoration: _buildHeaderDecoration(),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            Expanded(child: _buildSearchBox()),
            const SizedBox(width: 12),
            _buildCancelButton(),
          ],
        ),
      ),
    );
  }

  BoxDecoration _buildHeaderDecoration() {
    return const BoxDecoration(
      color: AppTheme.surfaceColor,
      boxShadow: [
        BoxShadow(
          color: AppTheme.shadowColor,
          offset: Offset(0, 2),
          blurRadius: 8,
        ),
      ],
    );
  }

  Widget _buildSearchBox() {
    return Container(
      height: 44,
      decoration: _buildSearchBoxDecoration(),
      child: TextField(
        controller: _searchController,
        focusNode: _focusNode,
        onChanged: widget.onSearchChanged,
        decoration: InputDecoration(
          hintText: '搜索知识库、笔记...',
          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.textSecondaryColor,
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: AppTheme.primaryColor,
            size: 20,
          ),
          suffixIcon: _searchController.text.isNotEmpty ? _buildClearButton() : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppTheme.textPrimaryColor,
        ),
      ),
    );
  }

  BoxDecoration _buildSearchBoxDecoration() {
    return BoxDecoration(
      color: AppTheme.searchBoxColor,
      borderRadius: BorderRadius.circular(22),
      boxShadow: const [
        BoxShadow(
          color: AppTheme.searchBoxShadow,
          offset: Offset(0, 1),
          blurRadius: 4,
          spreadRadius: 0,
        ),
      ],
    );
  }

  Widget _buildClearButton() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: _clearSearch,
        child: Container(
          padding: const EdgeInsets.all(8),
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppTheme.textSecondaryColor.withValues(alpha: 0.08),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.close_rounded,
              color: AppTheme.textSecondaryColor,
              size: 12,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCancelButton() {
    return TextButton(
      onPressed: widget.onCancel,
      child: Text(
        '取消',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../../models/knowledge_base.dart';
import '../../models/note.dart';
import '../../theme/app_theme.dart';
import 'search_result_item.dart';

class SearchResultsList extends StatelessWidget {
  final List<KnowledgeBase> knowledgeBases;
  final List<Note> notes;
  final Function(KnowledgeBase) onKnowledgeBaseTap;
  final Function(Note) onNoteTap;

  const SearchResultsList({
    super.key,
    required this.knowledgeBases,
    required this.notes,
    required this.onKnowledgeBaseTap,
    required this.onNoteTap,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildResultsCount(context),
          const SizedBox(height: 16),
          if (knowledgeBases.isNotEmpty) ...[
            _buildKnowledgeBasesSection(context),
            const SizedBox(height: 24),
          ],
          if (notes.isNotEmpty) _buildNotesSection(context),
        ],
      ),
    );
  }

  Widget _buildResultsCount(BuildContext context) {
    final totalCount = knowledgeBases.length + notes.length;
    return Text(
      '找到 $totalCount 个结果',
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: AppTheme.textSecondaryColor,
      ),
    );
  }

  Widget _buildKnowledgeBasesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(context, '知识库', knowledgeBases.length),
        const SizedBox(height: 12),
        ...knowledgeBases.map((kb) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: KnowledgeBaseSearchItem(
            knowledgeBase: kb,
            onTap: () => onKnowledgeBaseTap(kb),
          ),
        )),
      ],
    );
  }

  Widget _buildNotesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(context, '笔记', notes.length),
        const SizedBox(height: 12),
        ...notes.map((note) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: NoteSearchItem(
            note: note,
            onTap: () => onNoteTap(note),
          ),
        )),
      ],
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title, int count) {
    return Text(
      '$title ($count)',
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
      ),
    );
  }
}

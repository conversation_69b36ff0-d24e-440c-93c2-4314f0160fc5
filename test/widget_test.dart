// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:videoseek/main.dart';

void main() {
  testWidgets('App loads successfully', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const VideoSeekApp());

    // Verify that the app loads with search functionality
    expect(find.text('知识库'), findsOneWidget);
    expect(find.text('笔记'), findsOneWidget);

    // Verify search bar placeholder is present
    expect(find.text('搜索知识库、笔记...'), findsOneWidget);

    // Verify welcome banner is present
    expect(find.text('欢迎来到VideoSeek'), findsOneWidget);
    expect(find.text('现在，开始你的灵感之旅吧'), findsOneWidget);
    expect(find.text('聊一聊'), findsOneWidget);

    // Verify refresh indicator is present
    expect(find.byType(RefreshIndicator), findsOneWidget);
  });

  testWidgets('Search functionality', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const VideoSeekApp());

    // Find the search container
    final searchContainer = find.text('搜索知识库、笔记...');
    expect(searchContainer, findsOneWidget);

    // Tap on search container should navigate to search page
    await tester.tap(searchContainer);
    await tester.pumpAndSettle();

    // Should be on search page now
    expect(find.text('取消'), findsOneWidget);
  });

  testWidgets('Welcome banner navigation', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const VideoSeekApp());

    // Find the welcome banner
    final welcomeBanner = find.text('欢迎来到VideoSeek');
    expect(welcomeBanner, findsOneWidget);

    // Tap on welcome banner should navigate to chat page
    await tester.tap(welcomeBanner);
    await tester.pumpAndSettle();

    // Should be on chat page now
    expect(find.text('开始对话'), findsOneWidget);
  });

  testWidgets('Floating action buttons are present', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const VideoSeekApp());

    // Verify floating action buttons are present
    expect(find.text('更多'), findsOneWidget);
    expect(find.text('录音'), findsOneWidget);
    expect(find.text('文字'), findsOneWidget);

    // Verify icons are present
    expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
    expect(find.byIcon(Icons.mic), findsOneWidget);
    expect(find.byIcon(Icons.edit_outlined), findsOneWidget);
  });
}
